<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Dev Career Quiz</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            padding: 20px;
        }
        
        .quiz-container {
            max-width: 700px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .quiz-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .quiz-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 12px;
        }
        
        .quiz-subtitle {
            font-size: 1rem;
            color: #666;
            line-height: 1.5;
        }
        
        .progress-container {
            margin-bottom: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
            transition: width 0.4s ease;
            width: 12.5%;
        }
        
        .progress-text {
            font-size: 0.9rem;
            color: #888;
            text-align: center;
        }
        
        .question-container {
            margin-bottom: 30px;
        }
        
        .question-number {
            font-size: 0.9rem;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .question-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .question-hint {
            font-size: 0.85rem;
            color: #888;
            margin-bottom: 20px;
            font-style: italic;
        }
        
        .options-grid {
            display: grid;
            gap: 12px;
        }
        
        .option {
            padding: 16px 20px;
            border: 2px solid #e8e8e8;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            display: flex;
            align-items: center;
            gap: 14px;
            position: relative;
        }
        
        .option:hover {
            border-color: #667eea;
            background: #f8f9ff;
            transform: translateY(-1px);
        }
        
        .option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }
        
        .option-emoji {
            font-size: 1.4rem;
            flex-shrink: 0;
        }
        
        .option-text {
            font-size: 0.95rem;
            font-weight: 500;
            line-height: 1.4;
            flex: 1;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e8e8e8;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .results-container {
            text-align: center;
            padding: 20px 0;
        }
        
        .results-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 16px;
        }
        
        .results-role {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 16px;
        }
        
        .results-description {
            font-size: 1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 24px;
        }
        
        .score-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 12px;
            margin: 24px 0;
        }
        
        .score-item {
            padding: 16px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .score-label {
            font-size: 0.8rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
            text-transform: uppercase;
        }
        
        .score-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: #667eea;
        }
        
        @media (max-width: 768px) {
            .quiz-container {
                padding: 20px;
                margin: 10px;
            }
            
            .quiz-title {
                font-size: 1.8rem;
            }
            
            .question-title {
                font-size: 1.2rem;
            }
            
            .option {
                padding: 14px 16px;
            }
            
            .score-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1 class="quiz-title">🎮 Game Dev Career Quiz</h1>
            <p class="quiz-subtitle">Discover your ideal role in game development! Choose 1-2 answers per question for the most accurate results.</p>
        </div>
        
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">Question 1 of 8</div>
        </div>
        
        <div id="quizContent">
            <div class="question-container">
                <div class="question-number">QUESTION 1</div>
                <h2 class="question-title">What energizes you most when starting a new project?</h2>
                <div class="question-hint">Choose up to 2 options</div>
                <div class="options-grid">
                    <div class="option" onclick="selectOption(0, 0)">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Sketching out the big picture and overall vision</span>
                    </div>
                    <div class="option" onclick="selectOption(0, 1)">
                        <span class="option-emoji">⚙️</span>
                        <span class="option-text">Diving deep into technical architecture and problem-solving</span>
                    </div>
                    <div class="option" onclick="selectOption(0, 2)">
                        <span class="option-emoji">🎭</span>
                        <span class="option-text">Creating visual concepts and mood boards</span>
                    </div>
                    <div class="option" onclick="selectOption(0, 3)">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Understanding the audience and their needs</span>
                    </div>
                    <div class="option" onclick="selectOption(0, 4)">
                        <span class="option-emoji">📖</span>
                        <span class="option-text">Planning the narrative and emotional journey</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="navigation">
            <button class="btn btn-secondary" id="prevBtn" onclick="previousQuestion()" style="visibility: hidden;">Previous</button>
            <button class="btn btn-primary" id="nextBtn" onclick="nextQuestion()" disabled>Next</button>
        </div>
    </div>

    <script>
        const questions = [
            {
                title: "What energizes you most when starting a new project?",
                maxSelections: 2,
                options: [
                    { emoji: "🎨", text: "Sketching out the big picture and overall vision", scores: { creative: 3, business: 1, leadership: 2 } },
                    { emoji: "⚙️", text: "Diving deep into technical architecture and problem-solving", scores: { technical: 3, systems: 2, detail: 2 } },
                    { emoji: "🎭", text: "Creating visual concepts and mood boards", scores: { visual: 3, creative: 1, innovation: 1 } },
                    { emoji: "👥", text: "Understanding the audience and their needs", scores: { business: 2, social: 2, systems: 1, user_focus: 3 } },
                    { emoji: "📖", text: "Planning the narrative and emotional journey", scores: { narrative: 3, creative: 1, user_focus: 2 } }
                ]
            },
            {
                title: "How do you prefer to receive feedback?",
                maxSelections: 2,
                options: [
                    { emoji: "💻", text: "Technical peer review with specific implementation suggestions", scores: { technical: 3, detail: 2, collaboration: 1 } },
                    { emoji: "🎨", text: "Creative direction and artistic vision guidance", scores: { creative: 2, visual: 2, audio: 1, innovation: 2 } },
                    { emoji: "❤️", text: "User reactions and emotional responses to your work", scores: { narrative: 2, social: 2, user_focus: 3 } },
                    { emoji: "📈", text: "Data-driven insights and performance metrics", scores: { business: 3, systems: 2, detail: 2 } },
                    { emoji: "🚀", text: "Recognition for innovation and pushing boundaries", scores: { creative: 2, technical: 1, innovation: 3 } }
                ]
            }
        ];

        let currentQuestion = 0;
        let answers = [];
        let scores = {
            creative: 0, technical: 0, visual: 0, audio: 0, social: 0,
            business: 0, narrative: 0, systems: 0, leadership: 0,
            collaboration: 0, detail: 0, innovation: 0, user_focus: 0
        };

        function selectOption(questionIndex, optionIndex) {
            if (!answers[questionIndex]) answers[questionIndex] = [];
            
            const option = document.querySelectorAll('.option')[optionIndex];
            const isSelected = answers[questionIndex].includes(optionIndex);
            
            if (isSelected) {
                answers[questionIndex] = answers[questionIndex].filter(i => i !== optionIndex);
                option.classList.remove('selected');
            } else {
                if (answers[questionIndex].length < questions[questionIndex].maxSelections) {
                    answers[questionIndex].push(optionIndex);
                    option.classList.add('selected');
                }
            }
            
            document.getElementById('nextBtn').disabled = answers[questionIndex].length === 0;
        }

        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                currentQuestion++;
                showQuestion();
            } else {
                calculateResults();
            }
        }

        function previousQuestion() {
            if (currentQuestion > 0) {
                currentQuestion--;
                showQuestion();
            }
        }

        function showQuestion() {
            const question = questions[currentQuestion];
            const content = document.getElementById('quizContent');
            
            content.innerHTML = `
                <div class="question-container">
                    <div class="question-number">QUESTION ${currentQuestion + 1}</div>
                    <h2 class="question-title">${question.title}</h2>
                    <div class="question-hint">Choose up to ${question.maxSelections} option${question.maxSelections > 1 ? 's' : ''}</div>
                    <div class="options-grid">
                        ${question.options.map((option, index) => `
                            <div class="option" onclick="selectOption(${currentQuestion}, ${index})">
                                <span class="option-emoji">${option.emoji}</span>
                                <span class="option-text">${option.text}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            
            // Restore selections
            if (answers[currentQuestion]) {
                answers[currentQuestion].forEach(index => {
                    document.querySelectorAll('.option')[index].classList.add('selected');
                });
            }
            
            // Update navigation
            document.getElementById('prevBtn').style.visibility = currentQuestion > 0 ? 'visible' : 'hidden';
            document.getElementById('nextBtn').disabled = !answers[currentQuestion] || answers[currentQuestion].length === 0;
            document.getElementById('nextBtn').textContent = currentQuestion === questions.length - 1 ? 'Get Results' : 'Next';
            
            // Update progress
            const progress = ((currentQuestion + 1) / questions.length) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
            document.getElementById('progressText').textContent = `Question ${currentQuestion + 1} of ${questions.length}`;
        }

        function calculateResults() {
            // Calculate scores
            answers.forEach((selections, qIndex) => {
                if (selections) {
                    selections.forEach(optionIndex => {
                        const option = questions[qIndex].options[optionIndex];
                        Object.keys(option.scores).forEach(key => {
                            scores[key] += option.scores[key];
                        });
                    });
                }
            });
            
            showResults();
        }

        function showResults() {
            const topScores = Object.entries(scores)
                .filter(([key]) => ['creative', 'technical', 'visual', 'business', 'social', 'narrative'].includes(key))
                .sort(([,a], [,b]) => b - a);
            
            const primaryCategory = topScores[0][0];
            
            let role = "Game Developer";
            let description = "You have a balanced approach to game development with diverse interests and skills.";
            
            if (primaryCategory === 'technical') {
                role = scores.leadership >= 3 ? "Technical Director" : "Gameplay Programmer";
                description = scores.leadership >= 3 ? 
                    "You excel at technical leadership and system architecture. You're perfect for guiding technical teams." :
                    "You're passionate about bringing game mechanics to life through code.";
            } else if (primaryCategory === 'creative') {
                role = scores.visual >= 4 ? "Art Director" : "Game Designer";
                description = scores.visual >= 4 ?
                    "You have a strong vision for visual aesthetics and can guide artistic teams." :
                    "You have a natural talent for creating engaging gameplay experiences.";
            } else if (primaryCategory === 'visual') {
                role = "Visual Artist";
                description = "You have a strong eye for visual design and can create stunning artwork.";
            } else if (primaryCategory === 'business') {
                role = "Producer";
                description = "You excel at managing projects and ensuring games are delivered successfully.";
            } else if (primaryCategory === 'social') {
                role = "Community Manager";
                description = "You're great at building relationships with players and managing community engagement.";
            } else if (primaryCategory === 'narrative') {
                role = "Narrative Designer";
                description = "You excel at crafting compelling stories and meaningful player experiences.";
            }
            
            document.getElementById('quizContent').innerHTML = `
                <div class="results-container">
                    <h2 class="results-title">🎉 Your Results Are In!</h2>
                    <div class="results-role">${role}</div>
                    <p class="results-description">${description}</p>
                    
                    <div class="score-grid">
                        ${topScores.slice(0, 4).map(([category, score]) => `
                            <div class="score-item">
                                <div class="score-label">${category}</div>
                                <div class="score-value">${score}</div>
                            </div>
                        `).join('')}
                    </div>
                    
                    <button class="btn btn-primary" onclick="location.reload()" style="margin-top: 20px;">Take Quiz Again</button>
                </div>
            `;
            
            document.querySelector('.navigation').style.display = 'none';
        }
    </script>
</body>
</html>
