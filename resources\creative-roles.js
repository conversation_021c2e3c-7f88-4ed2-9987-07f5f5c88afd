// Creative Role Definitions for Game Dev Career Quiz
// Focus: Fundamentals-first approach, practical creative guidance

const creativeRoles = {
    "Art Director": {
        description: "You're the visual architect who defines how games look and feel. Like the art directors behind Journey, Ori and the Blind Forest, or Cuphead, you shape entire aesthetic experiences. You understand that art isn't just decoration - it's communication, emotion, and world-building all rolled into one.",
        
        strengths: [
            "Strong aesthetic vision",
            "Team leadership in creative contexts",
            "Visual storytelling ability", 
            "Style consistency maintenance"
        ],
        
        weaknesses: [
            "May be overly critical of artistic work",
            "Risk of perfectionism blocking progress",
            "Tendency to micromanage creative decisions"
        ],
        
        first30Days: [
            "Week 1: Create 5 different mood boards for the same game concept",
            "Week 2: Study 3 games with distinct art styles - document what makes them work",
            "Week 3: Practice giving constructive art feedback using specific terminology",
            "Week 4: Create a style guide for a hypothetical game project"
        ],
        
        portfolioEssentials: [
            "Complete art style guide with color palettes, lighting rules, and asset examples",
            "Before/after art direction examples showing visual problem-solving",
            "Mood boards and concept art that clearly communicate different game genres",
            "Team collaboration examples (art reviews, feedback documentation)",
            "Cross-media inspiration board showing influences from film, illustration, etc."
        ],
        
        fundamentalSkills: [
            "Master color theory and composition principles",
            "Learn to create comprehensive style guides and art bibles",
            "Understand lighting and mood in 3D environments",
            "Study art history and contemporary visual media",
            "Practice clear visual communication and presentation skills"
        ],
        
        bookRecommendation: "The Visual Story by Bruce Block",
        actionableAdvice: "Learn to communicate your vision through mood boards and style guides rather than just verbal direction. Trust your team's creativity while maintaining overall coherence. Set clear artistic boundaries but allow exploration within them."
    },

    "Creative Director": {
        description: "You're the creative mastermind who ensures all elements serve a unified vision. Think of directors like Amy Hennig (Uncharted), Ken Levine (BioShock), or Hideo Kojima (Metal Gear). You see how gameplay, story, art, and sound weave together to create experiences that players remember for years.",
        
        strengths: [
            "Holistic creative vision",
            "Narrative structure expertise",
            "Cross-disciplinary thinking",
            "Emotional design intuition"
        ],
        
        weaknesses: [
            "May struggle with technical constraints",
            "Risk of over-ambitious scope",
            "Tendency to change direction frequently"
        ],
        
        first30Days: [
            "Week 1: Write a one-page creative vision document for an original game concept",
            "Week 2: Analyze how 3 successful games integrate all their creative elements",
            "Week 3: Practice pitching your game concept in 2 minutes or less",
            "Week 4: Create a creative pillars document that guides all design decisions"
        ],
        
        portfolioEssentials: [
            "Complete game design document with unified creative vision",
            "Creative pillars document that clearly defines project goals",
            "Cross-disciplinary examples (how art supports narrative, etc.)",
            "Pitch materials that communicate vision to different audiences",
            "Case study of creative problem-solving under constraints"
        ],
        
        fundamentalSkills: [
            "Learn to write compelling creative vision documents",
            "Master the art of creative pitching and presentation",
            "Understand how different game disciplines interconnect",
            "Study successful creative directors and their methodologies",
            "Practice making creative decisions under time and budget constraints"
        ],
        
        bookRecommendation: "The Art of Computer Game Design by Chris Crawford",
        actionableAdvice: "Ground your creative vision in player psychology and technical reality. Create clear creative pillars that guide all decisions. Learn to say 'no' to good ideas that don't serve the core vision."
    },

    "Lead Game Designer": {
        description: "You're the design leader who coordinates systems and guides teams toward cohesive gameplay. Like the lead designers on games like Civilization, XCOM, or Overwatch, you balance creative vision with practical implementation, ensuring all systems work together harmoniously.",
        
        strengths: [
            "Systems design thinking",
            "Team coordination skills", 
            "Player psychology understanding",
            "Design communication ability"
        ],
        
        weaknesses: [
            "May over-design simple features",
            "Risk of design-by-committee",
            "Tendency to second-guess decisions"
        ],
        
        first30Days: [
            "Week 1: Design a simple game system with clear rules and interactions",
            "Week 2: Create a design document template that other designers can use",
            "Week 3: Practice running design reviews and giving constructive feedback",
            "Week 4: Study how successful games balance competing systems"
        ],
        
        portfolioEssentials: [
            "Complete game design with balanced, interconnected systems",
            "Design documentation that clearly communicates complex ideas",
            "Examples of iterative design process with playtesting feedback",
            "System balance analysis showing mathematical understanding",
            "Leadership examples (design reviews, team coordination)"
        ],
        
        fundamentalSkills: [
            "Master systems thinking and interconnected design",
            "Learn to write clear, actionable design documents",
            "Understand player psychology and motivation",
            "Study game balance and mathematical modeling",
            "Practice leading design discussions and making decisions"
        ],
        
        bookRecommendation: "The Art of Computer Game Design by Chris Crawford",
        actionableAdvice: "Develop strong playtesting instincts and trust them over theoretical design. Create clear design documents but be ready to iterate based on player feedback. Your role is to facilitate great design, not dictate it."
    },

    "Game Designer": {
        description: "You're the architect of fun - the person who crafts the rules, systems, and interactions that make games engaging. Whether designing levels like those in Portal, mechanics like those in Tetris, or progression systems like those in RPGs, you understand what makes players tick.",
        
        strengths: [
            "Gameplay intuition",
            "Iterative design mindset",
            "Player empathy",
            "System balance understanding"
        ],
        
        weaknesses: [
            "May over-iterate without shipping",
            "Risk of designing for yourself vs. audience",
            "Tendency to add complexity unnecessarily"
        ],
        
        first30Days: [
            "Week 1: Design and prototype a simple but engaging game mechanic",
            "Week 2: Playtest your mechanic with 5 different people and document feedback",
            "Week 3: Iterate on your design based on playtesting results",
            "Week 4: Create a level or scenario that teaches your mechanic naturally"
        ],
        
        portfolioEssentials: [
            "Original game mechanic with clear rules and engaging depth",
            "Level design examples showing progression and pacing",
            "Playtesting documentation with iteration examples",
            "Game balance analysis with mathematical backing",
            "Player psychology insights applied to design decisions"
        ],
        
        fundamentalSkills: [
            "Master rapid prototyping techniques (paper, digital tools)",
            "Learn to conduct effective playtesting sessions",
            "Understand game balance and progression curves",
            "Study player psychology and motivation theory",
            "Practice clear design communication and documentation"
        ],
        
        bookRecommendation: "The Art of Computer Game Design by Chris Crawford",
        actionableAdvice: "Playtest early and often with diverse audiences. Learn to recognize when a design is 'good enough' to ship and can be improved post-launch. Study games outside your preferred genres for fresh perspectives."
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = creativeRoles;
}
