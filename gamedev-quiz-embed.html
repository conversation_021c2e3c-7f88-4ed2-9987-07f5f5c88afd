<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Dev Career Quiz</title>
    <style>
        .quiz-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .quiz-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .quiz-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 10px;
        }
        
        .quiz-subtitle {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.5;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .question-container {
            margin-bottom: 30px;
        }
        
        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .question-type {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .options-container {
            display: grid;
            gap: 12px;
        }
        
        .option {
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .option-emoji {
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .option-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .question-counter {
            font-size: 0.9rem;
            color: #888;
        }
        
        .results-container {
            text-align: center;
            padding: 40px 20px;
        }
        
        .results-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 20px;
        }
        
        .results-role {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }
        
        .results-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .score-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .score-item {
            padding: 15px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .score-label {
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 5px;
        }
        
        .score-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        @media (max-width: 768px) {
            .quiz-container {
                margin: 10px;
                padding: 15px;
            }
            
            .quiz-title {
                font-size: 2rem;
            }
            
            .question-title {
                font-size: 1.3rem;
            }
            
            .option {
                padding: 12px 16px;
            }
            
            .score-breakdown {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1 class="quiz-title">🎮 Game Dev Career Quiz</h1>
            <p class="quiz-subtitle">Discover your ideal role in game development! Choose 1-2 answers per question for the most accurate results.</p>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        
        <div id="quizContent">
            <!-- Quiz questions will be inserted here -->
        </div>
        
        <div class="navigation">
            <button class="btn btn-secondary" id="prevBtn" onclick="previousQuestion()" style="display: none;">Previous</button>
            <span class="question-counter" id="questionCounter">Question 1 of 8</span>
            <button class="btn btn-primary" id="nextBtn" onclick="nextQuestion()" disabled>Next</button>
        </div>
    </div>

    <script>
        // Quiz data and scoring system
        const quizData = [
            {
                question: "What energizes you most when starting a new project?",
                type: "multiple",
                maxSelections: 2,
                options: [
                    {
                        emoji: "🎨",
                        text: "Sketching out the big picture and overall vision",
                        scores: { creative_score: 3, business_score: 1, leadership_preference: 2 }
                    },
                    {
                        emoji: "⚙️",
                        text: "Diving deep into technical architecture and problem-solving",
                        scores: { technical_score: 3, systems_score: 2, detail_orientation: 2 }
                    },
                    {
                        emoji: "🎭",
                        text: "Creating visual concepts and mood boards",
                        scores: { visual_score: 3, creative_score: 1, innovation_drive: 1 }
                    },
                    {
                        emoji: "👥",
                        text: "Understanding the audience and their needs",
                        scores: { business_score: 2, social_score: 2, systems_score: 1, user_focus: 3 }
                    },
                    {
                        emoji: "📖",
                        text: "Planning the narrative and emotional journey",
                        scores: { narrative_score: 3, creative_score: 1, user_focus: 2 }
                    }
                ]
            },
            {
                question: "How do you prefer to receive feedback?",
                type: "multiple",
                maxSelections: 2,
                options: [
                    {
                        emoji: "💻",
                        text: "Technical peer review with specific implementation suggestions",
                        scores: { technical_score: 3, detail_orientation: 2, collaboration_style: 1 }
                    },
                    {
                        emoji: "🎨",
                        text: "Creative direction and artistic vision guidance",
                        scores: { creative_score: 2, visual_score: 2, audio_score: 1, innovation_drive: 2 }
                    },
                    {
                        emoji: "❤️",
                        text: "User reactions and emotional responses to your work",
                        scores: { narrative_score: 2, social_score: 2, user_focus: 3 }
                    },
                    {
                        emoji: "📈",
                        text: "Data-driven insights and performance metrics",
                        scores: { business_score: 3, systems_score: 2, detail_orientation: 2 }
                    },
                    {
                        emoji: "🚀",
                        text: "Recognition for innovation and pushing boundaries",
                        scores: { creative_score: 2, technical_score: 1, innovation_drive: 3 }
                    }
                ]
            },
            {
                question: "What's your ideal team size?",
                type: "single",
                maxSelections: 1,
                options: [
                    {
                        emoji: "👤",
                        text: "Solo work (1 person)",
                        scores: { detail_orientation: 3, innovation_drive: 2, collaboration_style: -1 }
                    },
                    {
                        emoji: "👥",
                        text: "Small team (2-5 people)",
                        scores: { collaboration_style: 2, creative_score: 1, technical_score: 1 }
                    },
                    {
                        emoji: "👨‍👩‍👧‍👦",
                        text: "Medium team (6-15 people)",
                        scores: { social_score: 2, business_score: 1, collaboration_style: 2 }
                    },
                    {
                        emoji: "🏢",
                        text: "Large team (16+ people)",
                        scores: { business_score: 3, leadership_preference: 2, social_score: 1 }
                    },
                    {
                        emoji: "🔄",
                        text: "I adapt well to any team size",
                        scores: { collaboration_style: 3, social_score: 1 }
                    }
                ]
            },
            {
                question: "When do you do your best work?",
                type: "single",
                maxSelections: 1,
                options: [
                    {
                        emoji: "🌅",
                        text: "Early morning when it's quiet",
                        scores: { detail_orientation: 2, technical_score: 1, creative_score: 1 }
                    },
                    {
                        emoji: "☀️",
                        text: "During regular business hours with team energy",
                        scores: { collaboration_style: 3, social_score: 2, business_score: 1 }
                    },
                    {
                        emoji: "🌙",
                        text: "Late evening when inspiration strikes",
                        scores: { creative_score: 2, visual_score: 1, audio_score: 1, innovation_drive: 2 }
                    },
                    {
                        emoji: "⏰",
                        text: "I'm productive at any time with the right focus",
                        scores: { business_score: 1, collaboration_style: 1, detail_orientation: 1 }
                    },
                    {
                        emoji: "🔄",
                        text: "In intense bursts with breaks between",
                        scores: { innovation_drive: 2, creative_score: 2, technical_score: 1 }
                    }
                ]
            },
            {
                question: "What motivates you most in your career?",
                type: "multiple",
                maxSelections: 2,
                options: [
                    {
                        emoji: "🧩",
                        text: "Solving complex challenges that others can't",
                        scores: { technical_score: 3, innovation_drive: 2 }
                    },
                    {
                        emoji: "🎨",
                        text: "Bringing creative visions to life",
                        scores: { creative_score: 3, visual_score: 1, audio_score: 1 }
                    },
                    {
                        emoji: "❤️",
                        text: "Creating experiences that emotionally impact people",
                        scores: { narrative_score: 3, user_focus: 2 }
                    },
                    {
                        emoji: "📈",
                        text: "Building products that reach millions",
                        scores: { business_score: 3, social_score: 2 }
                    },
                    {
                        emoji: "👑",
                        text: "Leading teams and shaping company direction",
                        scores: { leadership_preference: 3, business_score: 2 }
                    }
                ]
            },
            {
                question: "How do you approach learning new skills?",
                type: "multiple",
                maxSelections: 2,
                options: [
                    {
                        emoji: "📚",
                        text: "Deep study of documentation and technical resources",
                        scores: { technical_score: 3, detail_orientation: 2 }
                    },
                    {
                        emoji: "🎨",
                        text: "Hands-on experimentation and creative exploration",
                        scores: { creative_score: 2, visual_score: 1, audio_score: 1, innovation_drive: 2 }
                    },
                    {
                        emoji: "👨‍🏫",
                        text: "Learning from mentors and peer collaboration",
                        scores: { social_score: 3, collaboration_style: 3 }
                    },
                    {
                        emoji: "📊",
                        text: "Analyzing case studies and industry examples",
                        scores: { business_score: 2, narrative_score: 1, systems_score: 1 }
                    },
                    {
                        emoji: "🔨",
                        text: "Building projects and learning through iteration",
                        scores: { technical_score: 2, systems_score: 2, innovation_drive: 1 }
                    }
                ]
            },
            {
                question: "What games do you find yourself drawn to?",
                type: "multiple",
                maxSelections: 2,
                options: [
                    {
                        emoji: "🎮",
                        text: "Technical showcases (realistic graphics, complex systems)",
                        scores: { technical_score: 2, visual_score: 2, detail_orientation: 1 }
                    },
                    {
                        emoji: "🎨",
                        text: "Artistic indies (unique visual style, creative gameplay)",
                        scores: { creative_score: 3, visual_score: 2, innovation_drive: 2 }
                    },
                    {
                        emoji: "📖",
                        text: "Story-rich RPGs (deep narrative, character development)",
                        scores: { narrative_score: 3, creative_score: 1, user_focus: 1 }
                    },
                    {
                        emoji: "🎵",
                        text: "Music/rhythm games (audio-focused, precision-based)",
                        scores: { audio_score: 3, detail_orientation: 2 }
                    },
                    {
                        emoji: "👥",
                        text: "Multiplayer competitive (community, balance, esports)",
                        scores: { social_score: 2, systems_score: 2, business_score: 1 }
                    },
                    {
                        emoji: "📱",
                        text: "Mobile/casual (accessible, broad appeal)",
                        scores: { business_score: 2, user_focus: 2, systems_score: 1 }
                    }
                ]
            },
            {
                question: "Choose your workflow preference:",
                type: "multiple",
                maxSelections: 2,
                options: [
                    {
                        emoji: "🏃‍♂️",
                        text: "Sprint-based with quick iterations",
                        scores: { technical_score: 2, business_score: 2, collaboration_style: 1 }
                    },
                    {
                        emoji: "🎨",
                        text: "Creative exploration with flexible timelines",
                        scores: { creative_score: 3, visual_score: 1, audio_score: 1, innovation_drive: 2 }
                    },
                    {
                        emoji: "📋",
                        text: "Structured phases with clear documentation",
                        scores: { business_score: 2, technical_score: 1, detail_orientation: 3 }
                    },
                    {
                        emoji: "🔄",
                        text: "Continuous feedback and user testing cycles",
                        scores: { systems_score: 2, user_focus: 3, social_score: 1 }
                    },
                    {
                        emoji: "🚀",
                        text: "Rapid prototyping and experimentation",
                        scores: { innovation_drive: 3, technical_score: 2, creative_score: 1 }
                    }
                ]
            }
        ];

        // Initialize scoring variables
        let scores = {
            creative_score: 0,
            technical_score: 0,
            visual_score: 0,
            audio_score: 0,
            social_score: 0,
            business_score: 0,
            narrative_score: 0,
            systems_score: 0,
            leadership_preference: 0,
            collaboration_style: 0,
            detail_orientation: 0,
            innovation_drive: 0,
            user_focus: 0
        };

        let currentQuestion = 0;
        let selectedAnswers = [];

        // Initialize quiz
        function initQuiz() {
            showQuestion(currentQuestion);
            updateProgress();
        }

        function showQuestion(questionIndex) {
            console.log('Showing question:', questionIndex);
            const question = quizData[questionIndex];
            console.log('Question data:', question);
            const quizContent = document.getElementById('quizContent');

            if (!question) {
                console.error('No question data for index:', questionIndex);
                return;
            }

            quizContent.innerHTML = `
                <div class="question-container">
                    <div class="question-type">Choose up to ${question.maxSelections} option${question.maxSelections > 1 ? 's' : ''}</div>
                    <h2 class="question-title">${question.question}</h2>
                    <div class="options-container">
                        ${question.options.map((option, index) => `
                            <div class="option" onclick="selectOption(${index})" data-option="${index}">
                                <span class="option-emoji">${option.emoji}</span>
                                <span class="option-text">${option.text}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            console.log('Question HTML set');

            // Restore previous selections
            if (selectedAnswers[questionIndex]) {
                selectedAnswers[questionIndex].forEach(optionIndex => {
                    const element = document.querySelector(`[data-option="${optionIndex}"]`);
                    if (element) {
                        element.classList.add('selected');
                    }
                });
            }

            updateNavigation();
        }

        function selectOption(optionIndex) {
            const question = quizData[currentQuestion];
            const optionElement = document.querySelector(`[data-option="${optionIndex}"]`);
            
            if (!selectedAnswers[currentQuestion]) {
                selectedAnswers[currentQuestion] = [];
            }
            
            const currentSelections = selectedAnswers[currentQuestion];
            const isSelected = currentSelections.includes(optionIndex);
            
            if (isSelected) {
                // Deselect
                selectedAnswers[currentQuestion] = currentSelections.filter(i => i !== optionIndex);
                optionElement.classList.remove('selected');
            } else {
                // Select (if under limit)
                if (currentSelections.length < question.maxSelections) {
                    selectedAnswers[currentQuestion].push(optionIndex);
                    optionElement.classList.add('selected');
                }
            }
            
            updateNavigation();
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const questionCounter = document.getElementById('questionCounter');
            
            prevBtn.style.display = currentQuestion > 0 ? 'block' : 'none';
            nextBtn.disabled = !selectedAnswers[currentQuestion] || selectedAnswers[currentQuestion].length === 0;
            
            if (currentQuestion === quizData.length - 1) {
                nextBtn.textContent = 'Get Results';
            } else {
                nextBtn.textContent = 'Next';
            }
            
            questionCounter.textContent = `Question ${currentQuestion + 1} of ${quizData.length}`;
        }

        function updateProgress() {
            const progress = ((currentQuestion + 1) / quizData.length) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        function nextQuestion() {
            if (currentQuestion < quizData.length - 1) {
                currentQuestion++;
                showQuestion(currentQuestion);
                updateProgress();
            } else {
                calculateResults();
            }
        }

        function previousQuestion() {
            if (currentQuestion > 0) {
                currentQuestion--;
                showQuestion(currentQuestion);
                updateProgress();
            }
        }

        function calculateResults() {
            // Calculate scores based on selected answers
            selectedAnswers.forEach((selections, questionIndex) => {
                if (selections) {
                    selections.forEach(optionIndex => {
                        const option = quizData[questionIndex].options[optionIndex];
                        Object.keys(option.scores).forEach(scoreKey => {
                            scores[scoreKey] += option.scores[scoreKey];
                        });
                    });
                }
            });
            
            showResults();
        }

        function showResults() {
            // Determine primary role based on highest scores
            const topCategories = Object.entries(scores)
                .filter(([key]) => ['creative_score', 'technical_score', 'visual_score', 'audio_score', 'social_score', 'business_score', 'narrative_score', 'systems_score'].includes(key))
                .sort(([,a], [,b]) => b - a);
            
            const primaryCategory = topCategories[0][0];
            const primaryScore = topCategories[0][1];
            
            // Role determination logic
            let role = "Game Developer";
            let description = "You have a balanced approach to game development with diverse interests and skills.";
            
            if (primaryCategory === 'technical_score') {
                if (scores.systems_score >= 8 && scores.leadership_preference >= 6) {
                    role = "Technical Director";
                    description = "You excel at technical leadership and system architecture. You're perfect for guiding technical teams and making high-level technical decisions.";
                } else if (scores.innovation_drive >= 8) {
                    role = "Tools Programmer";
                    description = "You love creating the tools that make game development possible. Your innovative approach helps streamline development workflows.";
                } else {
                    role = "Gameplay Programmer";
                    description = "You're passionate about bringing game mechanics to life through code. You excel at implementing the systems that make games fun and engaging.";
                }
            } else if (primaryCategory === 'creative_score') {
                if (scores.visual_score >= 8) {
                    role = "Art Director";
                    description = "You have a strong vision for visual aesthetics and can guide artistic teams to create cohesive, beautiful game worlds.";
                } else if (scores.narrative_score >= 8) {
                    role = "Creative Director";
                    description = "You excel at crafting compelling narratives and overall creative vision. You're perfect for guiding the creative direction of entire projects.";
                } else {
                    role = "Game Designer";
                    description = "You have a natural talent for creating engaging gameplay experiences and balancing creative vision with player needs.";
                }
            } else if (primaryCategory === 'visual_score') {
                if (scores.technical_score >= 6) {
                    role = "Technical Artist";
                    description = "You bridge the gap between art and technology, creating tools and workflows that help artists bring their visions to life efficiently.";
                } else {
                    role = "Visual Artist";
                    description = "You have a strong eye for visual design and can create stunning artwork that brings game worlds to life.";
                }
            } else if (primaryCategory === 'audio_score') {
                role = "Audio Designer";
                description = "You have a keen ear for sound and music, creating immersive audio experiences that enhance gameplay and storytelling.";
            } else if (primaryCategory === 'narrative_score') {
                if (scores.creative_score >= 8) {
                    role = "Narrative Designer";
                    description = "You excel at crafting compelling stories, memorable characters, and meaningful player choices that drive emotional engagement.";
                } else {
                    role = "Writer";
                    description = "You have a talent for creating engaging dialogue, lore, and narrative content that brings game worlds to life.";
                }
            } else if (primaryCategory === 'business_score') {
                if (scores.leadership_preference >= 8) {
                    role = "Executive Producer";
                    description = "You excel at high-level project management, strategic planning, and ensuring games meet business objectives while maintaining quality.";
                } else {
                    role = "Producer";
                    description = "You excel at managing projects, coordinating teams, and ensuring games are delivered successfully on time and budget.";
                }
            } else if (primaryCategory === 'social_score') {
                if (scores.business_score >= 6) {
                    role = "Community Manager";
                    description = "You're great at building and maintaining relationships with players, managing community engagement, and representing the game to its audience.";
                } else {
                    role = "QA Lead";
                    description = "You excel at coordinating testing efforts, communicating with development teams, and ensuring quality standards are met.";
                }
            } else if (primaryCategory === 'systems_score') {
                role = "Systems Designer";
                description = "You have a talent for creating balanced, interconnected game systems that create emergent and engaging gameplay experiences.";
            }
            
            const quizContent = document.getElementById('quizContent');
            quizContent.innerHTML = `
                <div class="results-container">
                    <h2 class="results-title">🎉 Your Results Are In!</h2>
                    <div class="results-role">${role}</div>
                    <p class="results-description">${description}</p>
                    
                    <div class="score-breakdown">
                        ${topCategories.slice(0, 4).map(([category, score]) => `
                            <div class="score-item">
                                <div class="score-label">${category.replace('_score', '').replace('_', ' ').toUpperCase()}</div>
                                <div class="score-value">${score}</div>
                            </div>
                        `).join('')}
                    </div>
                    
                    <button class="btn btn-primary" onclick="restartQuiz()" style="margin-top: 30px;">Take Quiz Again</button>
                </div>
            `;
            
            document.querySelector('.navigation').style.display = 'none';
        }

        function restartQuiz() {
            currentQuestion = 0;
            selectedAnswers = [];
            scores = {
                creative_score: 0,
                technical_score: 0,
                visual_score: 0,
                audio_score: 0,
                social_score: 0,
                business_score: 0,
                narrative_score: 0,
                systems_score: 0,
                leadership_preference: 0,
                collaboration_style: 0,
                detail_orientation: 0,
                innovation_drive: 0,
                user_focus: 0
            };
            
            document.querySelector('.navigation').style.display = 'flex';
            initQuiz();
        }

        // Start the quiz when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing quiz...');
            console.log('Quiz data:', quizData);
            initQuiz();
        });

        // Fallback initialization
        window.onload = function() {
            if (document.getElementById('quizContent').innerHTML === '') {
                console.log('Fallback initialization...');
                initQuiz();
            }
        };
    </script>
</body>
</html>
