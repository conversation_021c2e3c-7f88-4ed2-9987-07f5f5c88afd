// Personalized Resource Recommendation System
// Delivers customized guidance based on quiz results and user characteristics

const personalizationSystem = {
    // Customize recommendations based on user's score profile
    personalizeRecommendations: function(scores, role) {
        const personalizations = {};
        
        // Adjust first 30 days based on strengths/weaknesses
        personalizations.first30Days = this.customizeFirst30Days(scores, role);
        
        // Tailor portfolio recommendations
        personalizations.portfolioFocus = this.customizePortfolio(scores, role);
        
        // Adjust learning path based on background
        personalizations.learningPath = this.customizeLearningPath(scores, role);
        
        // Add personality-based insights
        personalizations.workStyle = this.generateWorkStyleInsights(scores);
        
        // Suggest complementary skills
        personalizations.complementarySkills = this.suggestComplementarySkills(scores, role);
        
        return personalizations;
    },

    customizeFirst30Days: function(scores, role) {
        const baseFirst30Days = this.getBaseFirst30Days(role);
        const customizations = [];
        
        // Adjust based on collaboration style
        if (scores.collaboration_style >= 8) {
            customizations.push("Focus on team-based projects and seek feedback early");
            customizations.push("Join online communities and share your progress");
        } else if (scores.collaboration_style <= 3) {
            customizations.push("Start with solo projects to build confidence");
            customizations.push("Document your work thoroughly for future collaboration");
        }
        
        // Adjust based on detail orientation
        if (scores.detail_orientation >= 8) {
            customizations.push("Take time to polish and document your work thoroughly");
            customizations.push("Create detailed project breakdowns and timelines");
        } else if (scores.detail_orientation <= 4) {
            customizations.push("Focus on completing projects rather than perfecting them");
            customizations.push("Set strict deadlines to avoid over-iteration");
        }
        
        // Adjust based on innovation drive
        if (scores.innovation_drive >= 8) {
            customizations.push("Experiment with cutting-edge techniques and tools");
            customizations.push("Try unconventional approaches to common problems");
        } else {
            customizations.push("Master proven techniques before exploring new ones");
            customizations.push("Study successful examples and understand why they work");
        }
        
        return {
            base: baseFirst30Days,
            personalizations: customizations
        };
    },

    customizePortfolio: function(scores, role) {
        const recommendations = [];
        
        // Technical vs Creative balance
        const techScore = scores.technical_score || 0;
        const creativeScore = scores.creative_score || 0;
        
        if (techScore > creativeScore + 3) {
            recommendations.push("Emphasize technical implementation details and performance metrics");
            recommendations.push("Include code samples and architectural diagrams");
            recommendations.push("Show problem-solving process and optimization techniques");
        } else if (creativeScore > techScore + 3) {
            recommendations.push("Lead with visual impact and creative concepts");
            recommendations.push("Show artistic process and iteration examples");
            recommendations.push("Demonstrate understanding of player psychology and emotion");
        } else {
            recommendations.push("Balance technical competence with creative vision");
            recommendations.push("Show how technical skills serve creative goals");
            recommendations.push("Demonstrate cross-disciplinary collaboration");
        }
        
        // Leadership preferences
        if (scores.leadership_preference >= 7) {
            recommendations.push("Include examples of mentoring, code reviews, or team coordination");
            recommendations.push("Document decision-making processes and their outcomes");
            recommendations.push("Show progression from individual contributor to team influence");
        }
        
        // User focus
        if (scores.user_focus >= 7) {
            recommendations.push("Include playtesting results and user feedback integration");
            recommendations.push("Show accessibility considerations in your designs");
            recommendations.push("Demonstrate understanding of different player types and needs");
        }
        
        return recommendations;
    },

    customizeLearningPath: function(scores, role) {
        const path = {
            immediate: [],
            shortTerm: [],
            longTerm: []
        };
        
        // Determine learning style preferences
        const systematicLearner = scores.detail_orientation >= 6;
        const experimentalLearner = scores.innovation_drive >= 6;
        const collaborativeLearner = scores.social_score >= 6;
        
        if (systematicLearner) {
            path.immediate.push("Start with comprehensive tutorials and documentation");
            path.immediate.push("Create detailed learning plans with measurable milestones");
            path.shortTerm.push("Build foundational knowledge before tackling advanced topics");
        }
        
        if (experimentalLearner) {
            path.immediate.push("Jump into hands-on projects and learn by doing");
            path.immediate.push("Experiment with different approaches to the same problem");
            path.shortTerm.push("Explore emerging technologies and unconventional techniques");
        }
        
        if (collaborativeLearner) {
            path.immediate.push("Find study groups or learning partners");
            path.immediate.push("Participate in community challenges and game jams");
            path.shortTerm.push("Seek mentorship opportunities and peer feedback");
        }
        
        // Role-specific learning priorities
        path.longTerm = this.getRoleLearningPriorities(role, scores);
        
        return path;
    },

    generateWorkStyleInsights: function(scores) {
        const insights = [];
        
        // Team size preferences
        if (scores.collaboration_style >= 8) {
            insights.push("You thrive in collaborative environments where you can share ideas and build on others' creativity. Look for studios that emphasize teamwork and cross-disciplinary collaboration.");
        } else if (scores.collaboration_style <= 3) {
            insights.push("You work best with clear individual responsibilities and minimal interruptions. Consider remote work opportunities or roles with significant independent work time.");
        }
        
        // Leadership tendencies
        if (scores.leadership_preference >= 8) {
            insights.push("Your leadership tendencies suggest you might also consider Lead or management positions as you gain experience. Start building mentoring skills early.");
        }
        
        // Innovation vs stability
        if (scores.innovation_drive >= 8) {
            insights.push("Your drive for innovation means you'd be particularly drawn to emerging technologies, experimental projects, or R&D roles within larger studios.");
        } else if (scores.innovation_drive <= 4) {
            insights.push("You prefer proven methodologies and stable workflows. Look for established studios with clear processes and mature development pipelines.");
        }
        
        // Detail orientation
        if (scores.detail_orientation >= 8) {
            insights.push("Your attention to detail makes you well-suited for roles requiring precision and thorough analysis. Consider specializing in optimization, QA, or technical documentation.");
        }
        
        // User focus
        if (scores.user_focus >= 8) {
            insights.push("Your strong user focus means you naturally consider the player experience in all your decisions. This makes you valuable for user research, accessibility, or community-facing roles.");
        }
        
        return insights;
    },

    suggestComplementarySkills: function(scores, role) {
        const suggestions = [];
        
        // Identify gaps and suggest complementary skills
        const skillGaps = this.identifySkillGaps(scores, role);
        
        skillGaps.forEach(gap => {
            switch(gap) {
                case 'technical':
                    suggestions.push("Consider learning basic programming or scripting to better communicate with technical team members");
                    break;
                case 'creative':
                    suggestions.push("Develop your visual communication skills through sketching, mood boards, or basic design principles");
                    break;
                case 'business':
                    suggestions.push("Learn about game monetization, player analytics, and project management to understand the business side");
                    break;
                case 'social':
                    suggestions.push("Practice presentation skills and learn to give constructive feedback to work better with teams");
                    break;
                case 'systems':
                    suggestions.push("Study how different game systems interact and influence each other");
                    break;
            }
        });
        
        return suggestions;
    },

    identifySkillGaps: function(scores, role) {
        const gaps = [];
        const threshold = 4; // Scores below this are considered gaps
        
        if (scores.technical_score < threshold && this.roleNeedsTechnical(role)) {
            gaps.push('technical');
        }
        if (scores.creative_score < threshold && this.roleNeedsCreative(role)) {
            gaps.push('creative');
        }
        if (scores.business_score < threshold && this.roleNeedsBusiness(role)) {
            gaps.push('business');
        }
        if (scores.social_score < threshold && this.roleNeedsSocial(role)) {
            gaps.push('social');
        }
        if (scores.systems_score < threshold && this.roleNeedsSystems(role)) {
            gaps.push('systems');
        }
        
        return gaps;
    },

    roleNeedsTechnical: function(role) {
        const technicalRoles = ['Technical Director', 'Tools Programmer', 'Engine Programmer', 'Gameplay Programmer', 'Technical Artist', 'Audio Programmer'];
        return technicalRoles.includes(role);
    },

    roleNeedsCreative: function(role) {
        const creativeRoles = ['Art Director', 'Creative Director', 'Game Designer', 'Visual Artist', 'Audio Designer', 'Narrative Designer', 'Writer'];
        return creativeRoles.includes(role);
    },

    roleNeedsBusiness: function(role) {
        const businessRoles = ['Executive Producer', 'Product Manager', 'Producer', 'Creative Director', 'Technical Director'];
        return businessRoles.includes(role);
    },

    roleNeedsSocial: function(role) {
        const socialRoles = ['Producer', 'Creative Director', 'Lead Game Designer', 'Community Manager', 'Team Lead'];
        return socialRoles.includes(role);
    },

    roleNeedsSystems: function(role) {
        const systemsRoles = ['Systems Programmer', 'Systems Designer', 'Technical Director', 'Game Designer', 'Producer'];
        return systemsRoles.includes(role);
    },

    getBaseFirst30Days: function(role) {
        // This would reference the base first30Days from role definitions
        // Implementation would pull from the role definition files
        return [];
    },

    getRoleLearningPriorities: function(role, scores) {
        // This would generate long-term learning priorities based on role and scores
        // Implementation would consider career progression paths
        return [];
    },

    // Generate experience level recommendations
    adjustForExperience: function(recommendations, experienceLevel) {
        if (experienceLevel === 'beginner') {
            return {
                ...recommendations,
                focus: 'fundamentals',
                timeframe: 'Take your time to build solid foundations',
                projects: 'Start with simple, completable projects'
            };
        } else if (experienceLevel === 'intermediate') {
            return {
                ...recommendations,
                focus: 'specialization',
                timeframe: 'Challenge yourself with more complex projects',
                projects: 'Build portfolio pieces that demonstrate depth'
            };
        } else {
            return {
                ...recommendations,
                focus: 'leadership',
                timeframe: 'Focus on impact and team contribution',
                projects: 'Lead projects that showcase your expertise'
            };
        }
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = personalizationSystem;
}
