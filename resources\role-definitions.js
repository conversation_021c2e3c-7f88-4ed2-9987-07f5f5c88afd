// Enhanced Role Definitions for Game Dev Career Quiz
// Focus: Fundamentals-first approach, no AI mentions, practical guidance

const enhancedRoles = {
    // TECHNICAL ROLES
    "Technical Director": {
        description: "You're the rare breed who can architect complex systems while keeping teams aligned and productive. Like the lead engineer on a AAA engine team, you see the big picture and the implementation details simultaneously. You're the person others turn to when the technical challenges seem impossible.",
        
        strengths: [
            "Exceptional systems thinking",
            "Natural technical leadership", 
            "Ability to translate complex concepts",
            "Strategic problem-solving"
        ],
        
        weaknesses: [
            "May become impatient with slower learners",
            "Risk of over-engineering solutions", 
            "Tendency to focus on perfection over delivery"
        ],
        
        first30Days: [
            "Week 1: Set up a personal game engine study project - start with a simple 2D renderer",
            "Week 2: Document your current technical projects with clear architecture diagrams", 
            "Week 3: Begin a performance profiling project on an existing open-source game",
            "Week 4: Start writing technical blog posts about your problem-solving process"
        ],
        
        portfolioEssentials: [
            "Custom engine component (physics, rendering, or audio system) with full source code",
            "Performance optimization case study with before/after metrics and methodology",
            "Technical design document for a complex system (multiplayer, procedural generation, etc.)",
            "Code review examples showing mentorship and technical communication skills",
            "Architecture diagram of a scalable game system with detailed explanations"
        ],
        
        fundamentalSkills: [
            "Master C++ memory management and performance optimization",
            "Understand graphics programming pipeline (vertex/fragment shaders)",
            "Learn multithreading and concurrent programming patterns",
            "Study data structures and algorithms specific to game development",
            "Practice technical writing and documentation skills"
        ],
        
        bookRecommendation: "The Mythical Man-Month by Frederick Brooks",
        actionableAdvice: "Develop your mentoring skills - your greatest impact will come through elevating entire teams. Practice explaining complex concepts in simple terms, and remember that sometimes 'good enough' solutions ship while perfect ones don't."
    },

    "Tools Programmer": {
        description: "You're the developer who builds the tools that make other developers' lives better. Think of the people who created Unity's editor, Unreal's Blueprint system, or Maya's scripting tools. You see workflow inefficiencies as boss fights to be conquered, and your solutions multiply entire teams' productivity.",
        
        strengths: [
            "Innovative problem-solving",
            "Workflow optimization mindset",
            "Empathy for developer pain points", 
            "Automation expertise"
        ],
        
        weaknesses: [
            "May over-optimize prematurely",
            "Risk of building tools nobody asked for",
            "Tendency to reinvent existing solutions"
        ],
        
        first30Days: [
            "Week 1: Interview 3 developers about their biggest workflow pain points",
            "Week 2: Build a simple automation script for a repetitive task you do daily",
            "Week 3: Study existing tools (Blender, Maya, Unity) - how do their UIs work?",
            "Week 4: Create a simple tool prototype that solves one specific problem"
        ],
        
        portfolioEssentials: [
            "Custom editor tool for Unity/Unreal with intuitive UI",
            "Automation script that saves hours of manual work (with time savings documented)",
            "Asset pipeline tool that converts/optimizes files automatically",
            "Debug visualization tool that makes complex data easy to understand",
            "Before/after workflow comparison showing productivity improvements"
        ],
        
        fundamentalSkills: [
            "Learn GUI programming (ImGui, Qt, or web-based tools)",
            "Master scripting languages (Python, C#, JavaScript)",
            "Understand file formats and data serialization",
            "Study user experience design principles",
            "Practice gathering and implementing user feedback"
        ],
        
        bookRecommendation: "The Pragmatic Programmer by David Thomas & Andrew Hunt",
        actionableAdvice: "Always validate tool ideas with actual users before building. Your superpower is making others more productive - focus on solving real pain points, not theoretical ones. Build MVPs and iterate based on feedback."
    },

    "Engine Programmer": {
        description: "You're drawn to the core systems that make games possible - the rendering pipeline, physics simulation, memory management. Like the engineers behind id Tech, Unreal Engine, or CryEngine, you find satisfaction in solving the hardest technical problems that enable everyone else's creativity.",
        
        strengths: [
            "Performance optimization expertise",
            "Low-level system understanding",
            "Mathematical problem-solving",
            "Attention to technical detail"
        ],
        
        weaknesses: [
            "May prioritize performance over readability",
            "Risk of premature optimization",
            "Tendency to work in isolation"
        ],
        
        first30Days: [
            "Week 1: Build a basic software renderer that draws triangles to screen",
            "Week 2: Implement a simple physics system (collision detection + response)",
            "Week 3: Create a memory allocator and profile its performance vs malloc",
            "Week 4: Study one open-source engine's architecture (Godot, Defold, etc.)"
        ],
        
        portfolioEssentials: [
            "Custom rendering engine with multiple shader techniques",
            "Physics simulation with realistic collision and dynamics",
            "Memory management system with performance comparisons",
            "Cross-platform abstraction layer (Windows/Mac/Linux)",
            "Detailed performance analysis of your systems vs commercial engines"
        ],
        
        fundamentalSkills: [
            "Master linear algebra and 3D mathematics",
            "Learn graphics APIs (OpenGL, DirectX, Vulkan basics)",
            "Understand computer architecture and cache optimization",
            "Study physics simulation and numerical methods",
            "Practice profiling and performance measurement"
        ],
        
        bookRecommendation: "Game Engine Architecture by Jason Gregory",
        actionableAdvice: "Balance your optimization instincts with practical deadlines. Document your complex systems thoroughly - future you (and your teammates) will thank you. Consider the maintainability cost of every optimization."
    },

    "Gameplay Programmer": {
        description: "You're the programmer who makes games fun. You implement the mechanics that players interact with directly - combat systems, character controllers, UI responsiveness. Your code is the bridge between design documents and player joy. You understand that clean code matters, but player experience matters more.",
        
        strengths: [
            "Game mechanics intuition",
            "Player experience focus",
            "Rapid prototyping ability",
            "Creative problem-solving"
        ],
        
        weaknesses: [
            "May sacrifice code quality for quick iteration",
            "Risk of feature creep",
            "Tendency to over-complicate simple mechanics"
        ],
        
        first30Days: [
            "Week 1: Recreate a classic game mechanic (Pac-Man movement, Tetris rotation)",
            "Week 2: Build a simple state machine for character behavior",
            "Week 3: Implement input handling with proper buffering and responsiveness",
            "Week 4: Create a juice-heavy prototype (screen shake, particles, sound feedback)"
        ],
        
        portfolioEssentials: [
            "Character controller that feels responsive and polished",
            "Combat system with clear feedback and satisfying impact",
            "UI system that's intuitive and accessible",
            "Game mechanic that's easy to learn but hard to master",
            "Prototype that demonstrates 'game feel' and player feedback"
        ],
        
        fundamentalSkills: [
            "Master state machines and behavior trees",
            "Learn input handling and player feedback systems",
            "Understand game loops and frame-rate independence",
            "Study game design patterns (Observer, Command, State)",
            "Practice rapid prototyping and iteration"
        ],
        
        bookRecommendation: "Game Programming Patterns by Robert Nystrom",
        actionableAdvice: "Embrace rapid prototyping but refactor ruthlessly. Your strength lies in making ideas playable quickly - use this to validate concepts early. Always playtest your implementations yourself."
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = enhancedRoles;
}
