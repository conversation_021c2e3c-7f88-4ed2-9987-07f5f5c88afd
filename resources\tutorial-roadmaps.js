// Tutorial Roadmaps for Game Development Roles
// Focus: Practical, progressive learning paths with specific project milestones

const tutorialRoadmaps = {
    "Technical Director": {
        beginner: [
            {
                title: "Build a Software Renderer",
                description: "Create a basic 2D renderer that draws triangles to screen without using graphics APIs",
                timeEstimate: "2-3 weeks",
                skills: ["Linear algebra", "Rasterization", "Memory management"],
                deliverable: "Working renderer that can draw colored triangles and basic shapes"
            },
            {
                title: "Implement a Component System",
                description: "Design and build an entity-component-system architecture",
                timeEstimate: "1-2 weeks", 
                skills: ["Software architecture", "Data-oriented design", "Performance optimization"],
                deliverable: "ECS framework with examples of game objects using components"
            }
        ],
        
        intermediate: [
            {
                title: "Performance Optimization Case Study",
                description: "Take a poorly performing game scene and optimize it systematically",
                timeEstimate: "2-4 weeks",
                skills: ["Profiling", "Bottleneck identification", "Optimization techniques"],
                deliverable: "Detailed report with before/after metrics and optimization strategies"
            },
            {
                title: "Build a Simple Game Engine",
                description: "Create a minimal but complete game engine with rendering, input, and audio",
                timeEstimate: "6-8 weeks",
                skills: ["Engine architecture", "Cross-platform development", "API design"],
                deliverable: "Working engine that can run a simple game like Pong or Breakout"
            }
        ],
        
        advanced: [
            {
                title: "Multithreaded Rendering Pipeline",
                description: "Implement a rendering system that uses multiple CPU cores effectively",
                timeEstimate: "4-6 weeks",
                skills: ["Multithreading", "Synchronization", "Lock-free programming"],
                deliverable: "Rendering system with measurable performance improvements on multi-core systems"
            }
        ]
    },

    "Tools Programmer": {
        beginner: [
            {
                title: "Asset Batch Processor",
                description: "Create a tool that processes multiple game assets automatically",
                timeEstimate: "1-2 weeks",
                skills: ["File I/O", "Batch processing", "User interface design"],
                deliverable: "Tool that can resize textures, convert formats, or optimize meshes in bulk"
            },
            {
                title: "Simple Level Editor",
                description: "Build a basic level editor for a 2D platformer game",
                timeEstimate: "3-4 weeks",
                skills: ["GUI programming", "Data serialization", "Tool design"],
                deliverable: "Working level editor that can create and save playable levels"
            }
        ],
        
        intermediate: [
            {
                title: "Maya/Blender Pipeline Tool",
                description: "Create a plugin that automates part of the art pipeline",
                timeEstimate: "2-3 weeks",
                skills: ["3D software scripting", "Pipeline optimization", "Artist workflow"],
                deliverable: "Plugin that solves a real workflow problem for 3D artists"
            },
            {
                title: "Build System Integration",
                description: "Create tools that integrate with continuous integration systems",
                timeEstimate: "2-4 weeks",
                skills: ["Build automation", "Version control", "DevOps practices"],
                deliverable: "Automated build pipeline with asset processing and validation"
            }
        ],
        
        advanced: [
            {
                title: "Visual Node Editor",
                description: "Build a node-based editor for game logic or shaders",
                timeEstimate: "6-8 weeks",
                skills: ["Advanced GUI programming", "Graph algorithms", "Code generation"],
                deliverable: "Working node editor that generates usable code or data"
            }
        ]
    },

    "Gameplay Programmer": {
        beginner: [
            {
                title: "Character Controller Collection",
                description: "Implement 5 different movement systems with polished feel",
                timeEstimate: "2-3 weeks",
                skills: ["Input handling", "Physics integration", "Game feel"],
                deliverable: "Demo showcasing platformer, FPS, racing, flying, and swimming controls"
            },
            {
                title: "State Machine Framework",
                description: "Build a reusable state machine system for game characters",
                timeEstimate: "1-2 weeks",
                skills: ["State management", "Design patterns", "Code organization"],
                deliverable: "State machine that can handle character AI, UI states, or game modes"
            }
        ],
        
        intermediate: [
            {
                title: "Combat System Demo",
                description: "Create a fighting game with frame data, hitboxes, and combos",
                timeEstimate: "4-6 weeks",
                skills: ["Collision detection", "Animation integration", "Game balance"],
                deliverable: "Playable fighting game with at least 2 characters and special moves"
            },
            {
                title: "Inventory and Crafting System",
                description: "Build a flexible inventory system with item crafting",
                timeEstimate: "3-4 weeks",
                skills: ["Data management", "UI programming", "System design"],
                deliverable: "Working inventory with drag-and-drop, item stacking, and recipe system"
            }
        ],
        
        advanced: [
            {
                title: "Multiplayer Gameplay System",
                description: "Implement networked gameplay with client prediction and lag compensation",
                timeEstimate: "6-10 weeks",
                skills: ["Network programming", "Client-server architecture", "Synchronization"],
                deliverable: "Multiplayer game that works smoothly over internet connections"
            }
        ]
    },

    "Game Designer": {
        beginner: [
            {
                title: "Mechanic Prototype Collection",
                description: "Create 5 unique game mechanics as simple, playable prototypes",
                timeEstimate: "2-3 weeks",
                skills: ["Rapid prototyping", "Core loop design", "Player feedback"],
                deliverable: "5 different mechanics that each demonstrate a unique player interaction"
            },
            {
                title: "Paper Prototype to Digital",
                description: "Design a board game, then convert it to a digital prototype",
                timeEstimate: "3-4 weeks",
                skills: ["Game balance", "Rule design", "Playtesting methodology"],
                deliverable: "Both physical and digital versions of the same game design"
            }
        ],
        
        intermediate: [
            {
                title: "Progression System Design",
                description: "Create a player progression system with meaningful choices",
                timeEstimate: "2-3 weeks",
                skills: ["Player psychology", "Mathematical modeling", "Reward systems"],
                deliverable: "Detailed progression system with skill trees, unlocks, and player agency"
            },
            {
                title: "Level Design Portfolio",
                description: "Design 3 levels that teach, challenge, and surprise players",
                timeEstimate: "4-6 weeks",
                skills: ["Spatial design", "Pacing", "Player guidance"],
                deliverable: "3 playable levels with design documentation explaining player journey"
            }
        ],
        
        advanced: [
            {
                title: "Emergent Gameplay System",
                description: "Design systems that create unexpected player stories",
                timeEstimate: "6-8 weeks",
                skills: ["Systems thinking", "Emergent design", "Player agency"],
                deliverable: "Game system that generates unique player stories through interaction"
            }
        ]
    },

    "Visual Artist": {
        beginner: [
            {
                title: "Style Study Collection",
                description: "Recreate art from 5 different games in their original styles",
                timeEstimate: "3-4 weeks",
                skills: ["Style analysis", "Color theory", "Composition"],
                deliverable: "5 pieces showing mastery of different artistic approaches"
            },
            {
                title: "Game Asset Pipeline",
                description: "Create a complete asset from concept to in-engine implementation",
                timeEstimate: "2-3 weeks",
                skills: ["3D modeling", "Texturing", "Technical constraints"],
                deliverable: "Game-ready asset with concept art, model, textures, and engine integration"
            }
        ],
        
        intermediate: [
            {
                title: "Environment Storytelling",
                description: "Create a game environment that tells a story without text",
                timeEstimate: "4-6 weeks",
                skills: ["Environmental design", "Visual narrative", "Lighting"],
                deliverable: "Complete environment that communicates story through visual elements alone"
            },
            {
                title: "Character Design Pipeline",
                description: "Design and model a character from concept to animation-ready",
                timeEstimate: "6-8 weeks",
                skills: ["Character design", "Anatomy", "Rigging preparation"],
                deliverable: "Complete character with concept art, model, textures, and basic rig"
            }
        ],
        
        advanced: [
            {
                title: "Art Direction Project",
                description: "Create a complete art bible for an original game concept",
                timeEstimate: "8-12 weeks",
                skills: ["Art direction", "Style consistency", "Team coordination"],
                deliverable: "Comprehensive art bible with style guides, color palettes, and asset examples"
            }
        ]
    },

    "Audio Designer": {
        beginner: [
            {
                title: "Sound Effect Library",
                description: "Record and edit 50 sound effects for common game actions",
                timeEstimate: "2-3 weeks",
                skills: ["Field recording", "Audio editing", "Sound design"],
                deliverable: "Professional-quality sound library organized by category"
            },
            {
                title: "Interactive Music System",
                description: "Create music that adapts to gameplay intensity",
                timeEstimate: "3-4 weeks",
                skills: ["Music composition", "Interactive audio", "Implementation"],
                deliverable: "Working music system that responds to player actions"
            }
        ],
        
        intermediate: [
            {
                title: "3D Audio Environment",
                description: "Design complete audio for a game level with spatial audio",
                timeEstimate: "4-6 weeks",
                skills: ["Spatial audio", "Environmental design", "Audio mixing"],
                deliverable: "Immersive audio environment with positional sounds and ambience"
            },
            {
                title: "Procedural Audio System",
                description: "Create sounds that generate dynamically based on game state",
                timeEstimate: "4-5 weeks",
                skills: ["Procedural generation", "Audio programming", "System design"],
                deliverable: "Audio system that creates unique sounds based on gameplay parameters"
            }
        ],
        
        advanced: [
            {
                title: "Complete Game Audio",
                description: "Handle all audio for a complete game project",
                timeEstimate: "10-16 weeks",
                skills: ["Audio direction", "Implementation", "Optimization"],
                deliverable: "Fully implemented audio for a complete game with music, SFX, and voice"
            }
        ]
    }
};

// Learning resources and prerequisites
const learningResources = {
    freeTools: [
        "Blender - Complete 3D creation suite",
        "Godot - Open-source game engine",
        "GIMP - Image editing software",
        "Audacity - Audio editing software",
        "Visual Studio Community - C++ development",
        "Unity Personal - Game engine (free tier)"
    ],
    
    fundamentalSkills: [
        "Mathematics - Linear algebra, trigonometry, basic calculus",
        "Programming - At least one language to intermediate level",
        "Art Fundamentals - Drawing, color theory, composition",
        "Design Thinking - Problem-solving and user empathy",
        "Communication - Technical writing and presentation skills"
    ],
    
    timeManagement: [
        "Start with beginner projects even if you have some experience",
        "Focus on completing projects rather than perfecting them",
        "Document your learning process and decisions",
        "Share work-in-progress for feedback",
        "Build a portfolio incrementally rather than waiting for perfection"
    ]
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { tutorialRoadmaps, learningResources };
}
