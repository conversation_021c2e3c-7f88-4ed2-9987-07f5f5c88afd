// 2025 Technology Stack Recommendations for Game Development Roles
// Focus: Current industry standards, avoiding AI hype, emphasizing fundamentals

const techStacks2025 = {
    // PROGRAMMING ROLES
    "Technical Director": {
        coreLanguages: [
            "C++ (C++17/20) - Industry standard for performance-critical systems",
            "C# - Unity development and rapid prototyping",
            "Python - Scripting, tools, and pipeline automation",
            "JavaScript/TypeScript - Web-based tools and dashboards"
        ],
        
        engines: [
            "Unreal Engine 5 - Industry-leading AAA development platform",
            "Unity 2023.x - Versatile cross-platform development",
            "Custom engines - Understanding core architecture principles"
        ],
        
        graphics: [
            "DirectX 12 - Modern Windows graphics programming",
            "Vulkan - Cross-platform, high-performance graphics API",
            "OpenGL - Legacy support and educational foundation",
            "Metal - iOS/macOS native graphics programming"
        ],
        
        tools: [
            "Visual Studio 2022 - Primary C++ development environment",
            "Git with LFS - Version control for large game projects",
            "Perforce - Industry standard for asset management",
            "CMake - Cross-platform build system management"
        ],
        
        profiling: [
            "Intel VTune Profiler - CPU performance analysis",
            "NVIDIA Nsight Graphics - GPU debugging and optimization",
            "Unity Profiler - Engine-specific performance analysis",
            "Custom profiling tools - Understanding measurement principles"
        ]
    },

    "Tools Programmer": {
        coreLanguages: [
            "C# - Unity editor tools and Windows applications",
            "Python - Maya/Blender scripting and pipeline automation",
            "JavaScript/TypeScript - Web-based tools and electron apps",
            "C++ - Performance-critical tool components"
        ],
        
        frameworks: [
            "Qt - Cross-platform desktop application development",
            "Dear ImGui - Immediate mode GUI for game tools",
            "Electron - Web-based desktop applications",
            "WPF - Windows-specific rich applications"
        ],
        
        automation: [
            "Jenkins - Continuous integration and build automation",
            "GitHub Actions - Git-integrated automation workflows",
            "PowerShell/Bash - System administration and scripting",
            "Docker - Containerized development environments"
        ],
        
        databases: [
            "SQLite - Lightweight embedded database for tools",
            "PostgreSQL - Robust relational database for larger tools",
            "JSON/YAML - Configuration and data interchange formats"
        ]
    },

    "Engine Programmer": {
        coreLanguages: [
            "C++ (Modern) - Core engine development language",
            "Assembly - Platform-specific optimizations",
            "HLSL/GLSL - Shader programming languages",
            "C - Low-level system programming"
        ],
        
        graphics: [
            "Vulkan - Modern, explicit graphics API",
            "DirectX 12 - Windows high-performance graphics",
            "Metal - Apple platform graphics programming",
            "OpenGL - Cross-platform graphics foundation"
        ],
        
        platforms: [
            "Windows SDK - Windows platform development",
            "PlayStation SDK - Console development (requires licensing)",
            "Xbox GDK - Microsoft console development",
            "Android NDK - Mobile native development"
        ],
        
        mathematics: [
            "GLM - OpenGL Mathematics library",
            "Eigen - Linear algebra library",
            "Custom math libraries - Understanding implementation details"
        ]
    },

    "Gameplay Programmer": {
        coreLanguages: [
            "C# - Unity gameplay programming",
            "C++ - Unreal Engine and performance-critical gameplay",
            "Blueprint - Unreal Engine visual scripting",
            "Lua - Embedded scripting for gameplay logic"
        ],
        
        engines: [
            "Unity 2023.x - Rapid prototyping and indie development",
            "Unreal Engine 5 - AAA gameplay development",
            "Godot - Open-source alternative with GDScript",
            "Custom engines - Understanding core gameplay systems"
        ],
        
        patterns: [
            "State Machines - Character and game state management",
            "Observer Pattern - Event-driven gameplay systems",
            "Command Pattern - Input handling and undo systems",
            "Component Systems - Modular gameplay architecture"
        ]
    },

    // CREATIVE ROLES
    "Technical Artist": {
        scripting: [
            "Python - Maya, Blender, and pipeline scripting",
            "MEL - Maya Embedded Language for Maya automation",
            "C# - Unity editor tools and custom inspectors",
            "HLSL/GLSL - Custom shader development"
        ],
        
        software: [
            "Maya 2024 - Industry standard 3D content creation",
            "Blender 4.x - Open-source 3D creation suite",
            "Substance Designer - Procedural material creation",
            "Houdini - Procedural modeling and effects"
        ],
        
        engines: [
            "Unity Shader Graph - Visual shader creation",
            "Unreal Material Editor - Node-based material creation",
            "Custom shader frameworks - Understanding implementation"
        ]
    },

    "Visual Artist": {
        modeling: [
            "Maya 2024 - Character and hard-surface modeling",
            "Blender 4.x - Free alternative with powerful features",
            "ZBrush - Digital sculpting and high-poly modeling",
            "3ds Max - Architectural and environment modeling"
        ],
        
        texturing: [
            "Substance Painter - Industry standard texture painting",
            "Substance Designer - Procedural material creation",
            "Photoshop 2024 - Traditional texture work and concept art",
            "Quixel Mixer - Material blending and creation"
        ],
        
        rendering: [
            "Arnold - High-quality offline rendering",
            "V-Ray - Architectural and product visualization",
            "Cycles - Blender's built-in rendering engine",
            "Real-time engines - Unity/Unreal for game assets"
        ]
    },

    // AUDIO ROLES
    "Audio Designer": {
        daws: [
            "Reaper - Affordable, powerful digital audio workstation",
            "Pro Tools - Industry standard for professional audio",
            "Logic Pro - Mac-based music production",
            "Ableton Live - Electronic music and live performance"
        ],
        
        middleware: [
            "Wwise - Industry standard interactive audio middleware",
            "FMOD - Popular alternative audio middleware",
            "Unity Audio - Built-in Unity audio system",
            "Unreal Audio - Built-in Unreal audio system"
        ],
        
        plugins: [
            "Native Instruments Komplete - Comprehensive sound library",
            "FabFilter Pro-Q - Professional EQ plugin",
            "Waves plugins - Industry standard audio processing",
            "Free alternatives - Understanding core audio principles"
        ]
    },

    "Audio Programmer": {
        languages: [
            "C++ - Real-time audio processing",
            "C# - Unity audio scripting",
            "Python - Audio tool development",
            "JUCE C++ - Cross-platform audio application framework"
        ],
        
        apis: [
            "DirectSound - Windows audio programming",
            "Core Audio - macOS/iOS audio programming",
            "ALSA - Linux audio programming",
            "Web Audio API - Browser-based audio applications"
        ],
        
        dsp: [
            "JUCE - Cross-platform audio application framework",
            "PortAudio - Cross-platform audio I/O library",
            "FFTW - Fast Fourier Transform library",
            "Custom DSP - Understanding signal processing fundamentals"
        ]
    },

    // BUSINESS ROLES
    "Producer": {
        management: [
            "Jira - Issue tracking and project management",
            "Confluence - Team documentation and knowledge sharing",
            "Slack/Discord - Team communication platforms",
            "Microsoft Project - Traditional project management"
        ],
        
        analytics: [
            "Excel/Google Sheets - Data analysis and reporting",
            "Tableau - Data visualization and business intelligence",
            "Unity Analytics - Game-specific player behavior tracking",
            "Custom dashboards - Understanding metrics that matter"
        ],
        
        versionControl: [
            "Git - Code version control",
            "Perforce - Asset version control for large teams",
            "PlasticSCM - Alternative version control system"
        ]
    },

    // DESIGN ROLES
    "Game Designer": {
        prototyping: [
            "Unity - Rapid digital prototyping",
            "Construct 3 - No-code game prototyping",
            "Twine - Interactive fiction and branching narratives",
            "Paper prototypes - Physical game design fundamentals"
        ],
        
        documentation: [
            "Confluence - Design documentation and wikis",
            "Notion - All-in-one workspace for design docs",
            "Google Docs - Collaborative document creation",
            "Miro/Figma - Visual design and flowchart creation"
        ],
        
        analysis: [
            "Excel/Google Sheets - Game balance and mathematical modeling",
            "Unity Analytics - Player behavior analysis",
            "Custom tools - Understanding what metrics matter"
        ]
    }
};

// Learning priorities for 2025
const learningPriorities2025 = {
    emerging: [
        "WebAssembly for cross-platform deployment",
        "Rust for performance-critical systems",
        "Real-time ray tracing implementation",
        "Procedural content generation techniques",
        "Accessibility-first design principles"
    ],
    
    fundamentals: [
        "Data structures and algorithms",
        "Computer graphics mathematics",
        "Software architecture patterns",
        "User experience design principles",
        "Project management methodologies"
    ],
    
    avoid: [
        "Overly specialized tools with small user bases",
        "Proprietary solutions without open alternatives",
        "Technologies without clear industry adoption",
        "Tools that solve problems you don't have yet"
    ]
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { techStacks2025, learningPriorities2025 };
}
