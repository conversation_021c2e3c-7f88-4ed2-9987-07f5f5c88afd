// Enhanced Book Recommendation Display System
// Creates rich, engaging book sections with affiliate links

const bookDisplaySystem = {
    // Generate HTML for book recommendation section
    generateBookSection: function(bookTitle, userAffiliateId) {
        const book = bookDatabase[bookTitle];
        if (!book) return '';
        
        // Replace placeholder with actual affiliate ID
        const affiliateLinks = this.updateAffiliateLinks(book.amazonLinks, userAffiliateId);
        
        return `
            <div class="book-recommendation">
                <div class="book-header">
                    <div class="book-cover">
                        <img src="https://images-na.ssl-images-amazon.com/images/P/${book.amazonASIN}.01.L.jpg" 
                             alt="${bookTitle} cover" 
                             loading="lazy"
                             onerror="this.style.display='none'">
                    </div>
                    <div class="book-info">
                        <h4 class="book-title">"${bookTitle}"</h4>
                        <p class="book-author">by ${book.author}</p>
                        <div class="book-meta">
                            <span class="book-pages">${book.pages} pages</span>
                            <span class="book-time">${book.timeToRead}</span>
                            <span class="book-difficulty">${book.difficulty}</span>
                        </div>
                    </div>
                </div>
                
                <div class="book-description">
                    <p><strong>Why this book is essential:</strong> ${book.whyEssential}</p>
                </div>
                
                <div class="book-takeaways">
                    <h5>Key Takeaways:</h5>
                    <ul>
                        ${book.keyTakeaways.map(takeaway => `<li>${takeaway}</li>`).join('')}
                    </ul>
                </div>
                
                <div class="book-purchase">
                    <div class="purchase-options">
                        ${this.generatePurchaseLinks(affiliateLinks)}
                    </div>
                    ${book.freeVersion ? `
                        <div class="free-option">
                            <p><strong>💡 Free Option:</strong> ${book.freeVersion}</p>
                        </div>
                    ` : ''}
                </div>
                
                <div class="affiliate-disclosure">
                    <small>📚 As an Amazon Associate, I earn from qualifying purchases. This helps support the creation of more career resources like this quiz!</small>
                </div>
            </div>
        `;
    },

    // Update affiliate links with user's actual ID
    updateAffiliateLinks: function(links, affiliateId) {
        const updatedLinks = {};
        for (const [format, url] of Object.entries(links)) {
            updatedLinks[format] = url.replace('YOURID-20', affiliateId);
        }
        return updatedLinks;
    },

    // Generate purchase link buttons
    generatePurchaseLinks: function(affiliateLinks) {
        const linkButtons = [];
        
        for (const [format, url] of Object.entries(affiliateLinks)) {
            const formatName = this.formatDisplayName(format);
            const icon = this.getFormatIcon(format);
            
            linkButtons.push(`
                <a href="${url}" 
                   target="_blank" 
                   rel="noopener" 
                   class="purchase-link ${format}-link"
                   onclick="trackBookClick('${format}')">
                    ${icon} ${formatName}
                </a>
            `);
        }
        
        return linkButtons.join('');
    },

    // Convert format keys to display names
    formatDisplayName: function(format) {
        const displayNames = {
            'paperback': 'Paperback',
            'hardcover': 'Hardcover',
            'kindle': 'Kindle',
            'audiobook': 'Audiobook'
        };
        return displayNames[format] || format;
    },

    // Get icons for different formats
    getFormatIcon: function(format) {
        const icons = {
            'paperback': '📖',
            'hardcover': '📚',
            'kindle': '📱',
            'audiobook': '🎧'
        };
        return icons[format] || '📄';
    },

    // Generate CSS for book recommendation styling
    generateBookCSS: function() {
        return `
            .book-recommendation {
                background: #f8f9ff;
                border: 2px solid #667eea;
                border-radius: 12px;
                padding: 24px;
                margin: 20px 0;
                font-family: inherit;
            }

            .book-header {
                display: flex;
                gap: 20px;
                margin-bottom: 20px;
                align-items: flex-start;
            }

            .book-cover {
                flex-shrink: 0;
                width: 80px;
            }

            .book-cover img {
                width: 100%;
                height: auto;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .book-info {
                flex: 1;
            }

            .book-title {
                margin: 0 0 8px 0;
                color: #1a1a1a;
                font-size: 1.3rem;
                font-weight: bold;
                line-height: 1.3;
            }

            .book-author {
                margin: 0 0 12px 0;
                color: #666;
                font-size: 1.1rem;
                font-style: italic;
            }

            .book-meta {
                display: flex;
                gap: 16px;
                flex-wrap: wrap;
                font-size: 0.9rem;
                color: #888;
            }

            .book-meta span {
                background: #e8f0fe;
                padding: 4px 8px;
                border-radius: 4px;
                font-weight: 500;
            }

            .book-description {
                margin-bottom: 20px;
                line-height: 1.6;
                color: #333;
            }

            .book-takeaways {
                margin-bottom: 24px;
            }

            .book-takeaways h5 {
                margin: 0 0 12px 0;
                color: #1a1a1a;
                font-size: 1.1rem;
            }

            .book-takeaways ul {
                margin: 0;
                padding-left: 20px;
                color: #444;
                line-height: 1.5;
            }

            .book-takeaways li {
                margin: 8px 0;
            }

            .book-purchase {
                margin-bottom: 16px;
            }

            .purchase-options {
                display: flex;
                gap: 12px;
                flex-wrap: wrap;
                margin-bottom: 12px;
            }

            .purchase-link {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                padding: 10px 16px;
                background: #667eea;
                color: white;
                text-decoration: none;
                border-radius: 6px;
                font-weight: 600;
                font-size: 0.95rem;
                transition: all 0.2s ease;
            }

            .purchase-link:hover {
                background: #5a6fd8;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                color: white;
                text-decoration: none;
            }

            .free-option {
                background: #e8f5e8;
                padding: 12px;
                border-radius: 6px;
                border-left: 4px solid #4caf50;
            }

            .free-option p {
                margin: 0;
                color: #2e7d32;
                font-size: 0.95rem;
            }

            .affiliate-disclosure {
                padding-top: 16px;
                border-top: 1px solid #ddd;
                text-align: center;
            }

            .affiliate-disclosure small {
                color: #666;
                font-size: 0.85rem;
                line-height: 1.4;
            }

            /* Mobile responsiveness */
            @media (max-width: 600px) {
                .book-header {
                    flex-direction: column;
                    text-align: center;
                }
                
                .book-cover {
                    align-self: center;
                }
                
                .purchase-options {
                    flex-direction: column;
                }
                
                .purchase-link {
                    justify-content: center;
                }
            }
        `;
    },

    // Generate JavaScript for tracking book clicks
    generateTrackingJS: function() {
        return `
            function trackBookClick(format) {
                // Track affiliate link clicks for analytics
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'book_click', {
                        'format': format,
                        'event_category': 'affiliate_links'
                    });
                }
                
                // You can add other analytics tracking here
                console.log('Book link clicked:', format);
            }
        `;
    },

    // Integration helper for quiz results
    integrateIntoQuizResults: function(role, userAffiliateId) {
        // Get the book recommendation for this role
        const roleBooks = this.getRoleBooksMapping();
        const bookTitle = roleBooks[role];
        
        if (!bookTitle) return '';
        
        return this.generateBookSection(bookTitle, userAffiliateId);
    },

    // Mapping of roles to their primary book recommendations
    getRoleBooksMapping: function() {
        return {
            "Technical Director": "The Mythical Man-Month",
            "Tools Programmer": "The Pragmatic Programmer",
            "Engine Programmer": "Game Engine Architecture",
            "Gameplay Programmer": "Game Programming Patterns",
            "Art Director": "The Visual Story",
            "Creative Director": "The Art of Computer Game Design",
            "Lead Game Designer": "The Art of Computer Game Design",
            "Game Designer": "The Art of Computer Game Design",
            "Visual Artist": "Color and Light",
            "Technical Artist": "Game Programming Patterns",
            "Audio Designer": "Game Programming Patterns", // Could add audio-specific book
            "Audio Programmer": "Game Programming Patterns",
            "Narrative Designer": "Character Development and Storytelling for Games",
            "Quest Designer": "The Hero with a Thousand Faces",
            "Writer": "The Writer's Journey",
            "Executive Producer": "Blood, Sweat, and Pixels",
            "Product Manager": "Inspired",
            "Producer": "Blood, Sweat, and Pixels"
        };
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = bookDisplaySystem;
}
