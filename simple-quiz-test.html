<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 2025 Game Dev Career Quiz</title>
    <style>
        .quiz-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .quiz-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .quiz-subtitle {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .question-type {
            font-size: 0.9rem;
            color: #888;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .options-container {
            display: grid;
            gap: 12px;
            margin-bottom: 30px;
        }
        
        .option {
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .option-emoji {
            font-size: 1.5rem;
            flex-shrink: 0;
        }
        
        .option-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.4;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #666;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .question-counter {
            font-size: 0.9rem;
            color: #888;
        }
        
        .results-container {
            text-align: center;
            padding: 40px 20px;
        }
        
        .results-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1a1a1a;
            margin-bottom: 20px;
        }
        
        .results-role {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }
        
        .results-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .score-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .score-item {
            padding: 15px;
            background: #f8f9ff;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .score-label {
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 5px;
        }
        
        .score-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        /* Enhanced Book Recommendation Styles */
        .book-recommendation {
            background: #f8f9ff;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            font-family: inherit;
        }

        .book-header {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: flex-start;
        }

        .book-cover {
            flex-shrink: 0;
            width: 80px;
        }

        .book-cover img {
            width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .book-info {
            flex: 1;
        }

        .book-title {
            margin: 0 0 8px 0;
            color: #1a1a1a;
            font-size: 1.3rem;
            font-weight: bold;
            line-height: 1.3;
        }

        .book-author {
            margin: 0 0 12px 0;
            color: #666;
            font-size: 1.1rem;
            font-style: italic;
        }

        .book-meta {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            font-size: 0.9rem;
            color: #888;
        }

        .book-meta span {
            background: #e8f0fe;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 500;
        }

        .book-description {
            margin-bottom: 20px;
            line-height: 1.6;
            color: #333;
        }

        .book-takeaways {
            margin-bottom: 24px;
        }

        .book-takeaways h5 {
            margin: 0 0 12px 0;
            color: #1a1a1a;
            font-size: 1.1rem;
        }

        .book-takeaways ul {
            margin: 0;
            padding-left: 20px;
            color: #444;
            line-height: 1.5;
        }

        .book-takeaways li {
            margin: 8px 0;
        }

        .book-purchase {
            margin-bottom: 16px;
        }

        .purchase-options {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 12px;
        }

        .purchase-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.2s ease;
        }

        .purchase-link:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        .free-option {
            background: #e8f5e8;
            padding: 12px;
            border-radius: 6px;
            border-left: 4px solid #4caf50;
        }

        .free-option p {
            margin: 0;
            color: #2e7d32;
            font-size: 0.95rem;
        }

        .affiliate-disclosure {
            padding-top: 16px;
            border-top: 1px solid #ddd;
            text-align: center;
        }

        .affiliate-disclosure small {
            color: #666;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .quiz-footer-disclosure {
            background: #f8f9fa;
            border-top: 2px solid #e9ecef;
            padding: 20px;
            margin-top: 40px;
            text-align: center;
            font-size: 0.9rem;
            color: #6c757d;
        }

        /* Ranking Question Styles */
        .ranking-container {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
        }

        .ranking-source, .ranking-target {
            flex: 1;
            min-height: 400px;
            padding: 20px;
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }

        .ranking-source h3, .ranking-target h3 {
            margin: 0 0 15px 0;
            font-size: 1.1rem;
            color: #1a1a1a;
            text-align: center;
        }

        .ranking-target {
            background: #f8f9ff;
            border-color: #667eea;
        }

        .ranking-item {
            padding: 12px 16px;
            margin: 8px 0;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: white;
            cursor: grab;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 12px;
            user-select: none;
        }

        .ranking-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        .ranking-item.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }

        .ranking-item.ranked {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .ranking-position {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .ranking-emoji {
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .ranking-text {
            font-size: 0.95rem;
            line-height: 1.3;
        }

        .drop-zone {
            min-height: 60px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            margin: 4px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .drop-zone.drag-over {
            border-color: #667eea;
            background: #f8f9ff;
            color: #667eea;
        }

        .drop-zone.filled {
            border: none;
            background: none;
            padding: 0;
            margin: 0;
        }

        /* Rating Question Styles */
        .rating-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 30px;
        }

        .rating-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            transition: all 0.2s ease;
        }

        .rating-item:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .rating-label {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .rating-emoji {
            font-size: 1.5rem;
            flex-shrink: 0;
        }

        .rating-text {
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.4;
        }

        .rating-scale {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .rating-scale input[type="range"] {
            width: 120px;
            height: 6px;
            border-radius: 3px;
            background: #e0e0e0;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .rating-scale input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .rating-scale input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .rating-value {
            min-width: 20px;
            text-align: center;
            font-weight: bold;
            color: #667eea;
            font-size: 1.1rem;
        }

        .rating-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #888;
            margin-top: 4px;
        }

        /* Picture Choice Styles */
        .picture-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .picture-option {
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            text-align: center;
            position: relative;
        }

        .picture-option:hover {
            border-color: #667eea;
            background: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        .picture-option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .picture-emoji {
            font-size: 2.5rem;
            margin-bottom: 10px;
            display: block;
        }

        .picture-text {
            font-size: 0.95rem;
            font-weight: 500;
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div class="quiz-container">
        <div class="quiz-header">
            <h1 class="quiz-title">🎮 2025 Game Dev Career Quiz</h1>
            <p class="quiz-subtitle">Discover how your mind naturally works and what type of game development thinking you have! No experience needed - just answer based on what interests you most. Choose 1-2 answers per question for the best results.</p>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 12.5%;"></div>
        </div>
        
        <div id="quizContent">
            <div class="question-container">
                <div class="question-type">Choose up to 2 options</div>
                <h2 class="question-title">What energizes you most when starting a new project?</h2>
                <div class="options-container">
                    <div class="option" onclick="selectOption(0)" data-option="0">
                        <span class="option-emoji">🎨</span>
                        <span class="option-text">Sketching out the big picture and overall vision</span>
                    </div>
                    <div class="option" onclick="selectOption(1)" data-option="1">
                        <span class="option-emoji">⚙️</span>
                        <span class="option-text">Diving deep into technical architecture and problem-solving</span>
                    </div>
                    <div class="option" onclick="selectOption(2)" data-option="2">
                        <span class="option-emoji">🎭</span>
                        <span class="option-text">Creating visual concepts and mood boards</span>
                    </div>
                    <div class="option" onclick="selectOption(3)" data-option="3">
                        <span class="option-emoji">👥</span>
                        <span class="option-text">Understanding the audience and their needs</span>
                    </div>
                    <div class="option" onclick="selectOption(4)" data-option="4">
                        <span class="option-emoji">📖</span>
                        <span class="option-text">Planning the narrative and emotional journey</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="navigation">
            <button class="btn btn-secondary" id="prevBtn" onclick="previousQuestion()" style="display: none;">Previous</button>
            <span class="question-counter" id="questionCounter">Question 1 of 11</span>
            <button class="btn btn-primary" id="nextBtn" onclick="nextQuestion()" disabled>Next</button>
        </div>
    </div>

    <script>
        // Enhanced Book Database with Affiliate Links
        const bookDatabase = {
            "The Mythical Man-Month": {
                author: "Frederick Brooks",
                amazonLinks: {
                    hardcover: "https://amzn.to/408cgnc",
                    kindle: "https://amzn.to/44KJFWr",
                    audiobook: "https://amzn.to/44S7cpw"
                },
                whyEssential: "The definitive guide to managing software projects and technical teams. Brooks' insights on communication, team scaling, and project management are as relevant today as when first published. Essential for anyone moving into technical leadership.",
                keyTakeaways: [
                    "Adding people to a late project makes it later",
                    "The importance of conceptual integrity in system design",
                    "Communication overhead grows exponentially with team size",
                    "The difference between essential and accidental complexity"
                ],
                difficulty: "Intermediate",
                timeToRead: "8-12 hours",
                amazonASIN: "0201835959"
            },
            "Game Programming Patterns": {
                author: "Robert Nystrom",
                amazonLinks: {
                    paperback: "https://amzn.to/46yfa8s",
                    kindle: "https://amzn.to/4ksEjVu"
                },
                whyEssential: "The perfect bridge between general programming patterns and game-specific implementations. Nystrom explains complex concepts with clear examples and practical game development scenarios. A must-read for any gameplay programmer.",
                keyTakeaways: [
                    "When and how to apply classic design patterns in games",
                    "Game-specific patterns like Update, Component, and Service Locator",
                    "Performance considerations for real-time systems",
                    "Clean code practices for game development"
                ],
                difficulty: "Beginner to Intermediate",
                timeToRead: "10-15 hours",
                freeVersion: "Available free online at gameprogrammingpatterns.com",
                amazonASIN: "0990582906"
            },
            "Game Engine Architecture": {
                author: "Jason Gregory",
                amazonLinks: {
                    hardcover: "https://amzn.to/3YJhJJF",
                    kindle: "https://amzn.to/3YJhJJF"
                },
                whyEssential: "The comprehensive guide to game engine systems and architecture. Gregory covers everything from low-level engine systems to high-level game frameworks. Essential for understanding how modern game engines work.",
                keyTakeaways: [
                    "Complete overview of game engine subsystems",
                    "Performance optimization techniques for real-time systems",
                    "Memory management strategies for games",
                    "Rendering pipeline and graphics programming concepts"
                ],
                difficulty: "Advanced",
                timeToRead: "25-40 hours",
                amazonASIN: "1138035459"
            },
            "The Pragmatic Programmer": {
                author: "David Thomas & Andrew Hunt",
                amazonLinks: {
                    paperback: "https://amzn.to/3YJhJJF",
                    kindle: "https://amzn.to/3YJhJJF",
                    audiobook: "https://amzn.to/3YJhJJF"
                },
                whyEssential: "Timeless advice on becoming a better programmer and problem solver. The principles in this book apply directly to game development and will make you more effective regardless of your specific role.",
                keyTakeaways: [
                    "DRY principle and code organization",
                    "Debugging techniques and problem-solving approaches",
                    "Tool mastery and automation",
                    "Professional development and continuous learning"
                ],
                difficulty: "Beginner to Intermediate",
                timeToRead: "8-12 hours",
                amazonASIN: "0135957052"
            },
            "The Art of Computer Game Design": {
                author: "Chris Crawford",
                amazonLinks: {
                    paperback: "https://amzn.to/3YJhJJF",
                    kindle: "https://amzn.to/3YJhJJF"
                },
                whyEssential: "The foundational text on game design philosophy. Crawford's insights into what makes games engaging and meaningful remain relevant decades later. Essential reading for understanding the art and craft of game design.",
                keyTakeaways: [
                    "The fundamental nature of games and play",
                    "What separates games from other forms of entertainment",
                    "The importance of meaningful player choices",
                    "Design philosophy and creative vision"
                ],
                difficulty: "Beginner",
                timeToRead: "3-5 hours",
                freeVersion: "Available free online at Chris Crawford's website",
                amazonASIN: "0881341177"
            },
            "The Visual Story": {
                author: "Bruce Block",
                amazonLinks: {
                    paperback: "https://amzn.to/3YJhJJF",
                    kindle: "https://amzn.to/3YJhJJF"
                },
                whyEssential: "Essential for understanding visual storytelling and composition. While focused on film, the principles apply directly to game cinematics, level design, and visual communication. Perfect for art directors and visual designers.",
                keyTakeaways: [
                    "Visual components: space, line, shape, tone, color, movement",
                    "How visual elements create emotional responses",
                    "Composition techniques for storytelling",
                    "Visual intensity and audience engagement"
                ],
                difficulty: "Intermediate",
                timeToRead: "8-12 hours",
                amazonASIN: "113801415X"
            },
            "Color and Light": {
                author: "James Gurney",
                amazonLinks: {
                    hardcover: "https://amzn.to/3YJhJJF"
                },
                whyEssential: "The definitive guide to understanding color and lighting for artists. Gurney's practical approach to color theory and lighting principles is invaluable for game artists working in any medium.",
                keyTakeaways: [
                    "Color relationships and harmony",
                    "Light behavior and atmospheric effects",
                    "Practical color mixing and application",
                    "Environmental lighting and mood creation"
                ],
                difficulty: "Intermediate",
                timeToRead: "6-10 hours",
                amazonASIN: "0740797719"
            },
            "Character Development and Storytelling for Games": {
                author: "Lee Sheldon",
                amazonLinks: {
                    paperback: "https://amzn.to/3YJhJJF",
                    kindle: "https://amzn.to/3YJhJJF"
                },
                whyEssential: "The comprehensive guide to interactive storytelling and character development in games. Sheldon covers both traditional narrative techniques and game-specific approaches to story design.",
                keyTakeaways: [
                    "Character development techniques for interactive media",
                    "Branching narrative design and player agency",
                    "Dialogue writing for games",
                    "Environmental storytelling and world-building"
                ],
                difficulty: "Intermediate",
                timeToRead: "12-18 hours",
                amazonASIN: "1435461045"
            },
            "Blood, Sweat, and Pixels": {
                author: "Jason Schreier",
                amazonLinks: {
                    hardcover: "https://amzn.to/3YJhJJF",
                    kindle: "https://amzn.to/3YJhJJF",
                    audiobook: "https://amzn.to/3YJhJJF"
                },
                whyEssential: "Essential reading for understanding the realities of game development. Schreier's behind-the-scenes look at major game productions reveals the challenges, triumphs, and human cost of making games.",
                keyTakeaways: [
                    "Real-world game development challenges",
                    "How successful games overcome production obstacles",
                    "The human side of game development",
                    "Project management lessons from shipped games"
                ],
                difficulty: "Beginner",
                timeToRead: "8-12 hours",
                amazonASIN: "0062651234"
            },
            "Inspired": {
                author: "Marty Cagan",
                amazonLinks: {
                    paperback: "https://amzn.to/3YJhJJF",
                    kindle: "https://amzn.to/3YJhJJF",
                    audiobook: "https://amzn.to/3YJhJJF"
                },
                whyEssential: "The definitive guide to product management in technology companies. Cagan's insights on building products that customers love apply directly to game development and live-service games.",
                keyTakeaways: [
                    "Product discovery vs. product delivery",
                    "User research and validation techniques",
                    "Building strong product teams",
                    "Balancing user needs with business goals"
                ],
                difficulty: "Intermediate",
                timeToRead: "10-15 hours",
                amazonASIN: "1119387507"
            }
        };

        // Role to Book Mapping
        const roleBookMapping = {
            "Technical Director": "The Mythical Man-Month",
            "Tools Programmer": "The Pragmatic Programmer",
            "Engine Programmer": "Game Engine Architecture",
            "Gameplay Programmer": "Game Programming Patterns",
            "Art Director": "The Visual Story",
            "Creative Director": "The Art of Computer Game Design",
            "Lead Game Designer": "The Art of Computer Game Design",
            "Game Designer": "The Art of Computer Game Design",
            "Visual Artist": "Color and Light",
            "Technical Artist": "Game Programming Patterns",
            "Audio Designer": "Game Programming Patterns",
            "Audio Programmer": "Game Programming Patterns",
            "Narrative Designer": "Character Development and Storytelling for Games",
            "Quest Designer": "Character Development and Storytelling for Games",
            "Writer": "Character Development and Storytelling for Games",
            "Executive Producer": "Blood, Sweat, and Pixels",
            "Product Manager": "Inspired",
            "Producer": "Blood, Sweat, and Pixels"
        };

        // Complete quiz data with all 12 questions
        const quizData = [
            {
                question: "When you imagine starting a creative project, what excites you most?",
                maxSelections: 2,
                options: [
                    { emoji: "🎨", text: "Imagining the big picture and how everything will look and feel", scores: { creative_score: 3, business_score: 1, leadership_preference: 2 } },
                    { emoji: "⚙️", text: "Figuring out how things work and solving complex puzzles", scores: { technical_score: 3, systems_score: 2, detail_orientation: 2 } },
                    { emoji: "🎭", text: "Creating beautiful visuals and artistic concepts", scores: { visual_score: 3, creative_score: 1, innovation_drive: 1 } },
                    { emoji: "👥", text: "Understanding what people want and how they'll react", scores: { business_score: 2, social_score: 2, systems_score: 1, user_focus: 3 } },
                    { emoji: "📖", text: "Crafting stories and emotional experiences", scores: { narrative_score: 3, creative_score: 1, user_focus: 2 } }
                ]
            },
            {
                question: "Rank these activities by what sounds most interesting to you",
                type: "ranking",
                maxSelections: 3,
                instructions: "Drag to rank your top 3 preferences (1st choice = most preferred)",
                options: [
                    {
                        emoji: "🔧",
                        text: "Solving puzzles and figuring out how things work",
                        scores: {
                            rank1: { technical_score: 3, detail_orientation: 2 },
                            rank2: { technical_score: 2, systems_score: 1 },
                            rank3: { technical_score: 1 }
                        }
                    },
                    {
                        emoji: "🎨",
                        text: "Drawing, designing, and creating beautiful visuals",
                        scores: {
                            rank1: { visual_score: 3, creative_score: 2 },
                            rank2: { visual_score: 2, innovation_drive: 1 },
                            rank3: { creative_score: 1 }
                        }
                    },
                    {
                        emoji: "📖",
                        text: "Writing stories and developing characters",
                        scores: {
                            rank1: { narrative_score: 3, user_focus: 2 },
                            rank2: { narrative_score: 2, creative_score: 1 },
                            rank3: { narrative_score: 1 }
                        }
                    },
                    {
                        emoji: "📊",
                        text: "Looking at data and finding ways to improve things",
                        scores: {
                            rank1: { systems_score: 3, business_score: 2 },
                            rank2: { systems_score: 2, detail_orientation: 1 },
                            rank3: { business_score: 1 }
                        }
                    },
                    {
                        emoji: "🎵",
                        text: "Composing music and designing audio",
                        scores: {
                            rank1: { audio_score: 3, creative_score: 1 },
                            rank2: { audio_score: 2, innovation_drive: 1 },
                            rank3: { audio_score: 1 }
                        }
                    },
                    {
                        emoji: "🤝",
                        text: "Organizing people and bringing teams together",
                        scores: {
                            rank1: { social_score: 3, leadership_preference: 3 },
                            rank2: { business_score: 2, collaboration_style: 2 },
                            rank3: { social_score: 1, leadership_preference: 1 }
                        }
                    }
                ]
            },
            {
                question: "How do you prefer to receive feedback?",
                maxSelections: 2,
                options: [
                    { emoji: "💻", text: "Technical peer review with specific implementation suggestions", scores: { technical_score: 3, detail_orientation: 2, collaboration_style: 1 } },
                    { emoji: "🎨", text: "Creative direction and artistic vision guidance", scores: { creative_score: 2, visual_score: 2, audio_score: 1, innovation_drive: 2 } },
                    { emoji: "❤️", text: "User reactions and emotional responses to your work", scores: { narrative_score: 2, social_score: 2, user_focus: 3 } },
                    { emoji: "📈", text: "Data-driven insights and performance metrics", scores: { business_score: 3, systems_score: 2, detail_orientation: 2 } },
                    { emoji: "🚀", text: "Recognition for innovation and pushing boundaries", scores: { creative_score: 2, technical_score: 1, innovation_drive: 3 } }
                ]
            },
            {
                question: "How many people would you want to work with on a creative project?",
                maxSelections: 1,
                options: [
                    { emoji: "👤", text: "Just me - I like working alone and focusing deeply", scores: { detail_orientation: 3, innovation_drive: 2, collaboration_style: -1 } },
                    { emoji: "👥", text: "A few close collaborators (2-5 people)", scores: { collaboration_style: 2, creative_score: 1, technical_score: 1 } },
                    { emoji: "👨‍👩‍👧‍👦", text: "A medium group where everyone knows each other (6-15 people)", scores: { social_score: 2, business_score: 1, collaboration_style: 2 } },
                    { emoji: "🏢", text: "A big team with lots of specialists (16+ people)", scores: { business_score: 3, leadership_preference: 2, social_score: 1 } },
                    { emoji: "🔄", text: "I'm flexible - any size works for me", scores: { collaboration_style: 3, social_score: 1 } }
                ]
            },
            {
                question: "When do you do your best work?",
                maxSelections: 1,
                options: [
                    { emoji: "🌅", text: "Early morning when it's quiet", scores: { detail_orientation: 2, technical_score: 1, creative_score: 1 } },
                    { emoji: "☀️", text: "During regular business hours with team energy", scores: { collaboration_style: 3, social_score: 2, business_score: 1 } },
                    { emoji: "🌙", text: "Late evening when inspiration strikes", scores: { creative_score: 2, visual_score: 1, audio_score: 1, innovation_drive: 2 } },
                    { emoji: "⏰", text: "I'm productive at any time with the right focus", scores: { business_score: 1, collaboration_style: 1, detail_orientation: 1 } },
                    { emoji: "🔄", text: "In intense bursts with breaks between", scores: { innovation_drive: 2, creative_score: 2, technical_score: 1 } }
                ]
            },
            {
                question: "What motivates you most in your career?",
                maxSelections: 2,
                options: [
                    { emoji: "🧩", text: "Solving complex challenges that others can't", scores: { technical_score: 3, innovation_drive: 2 } },
                    { emoji: "🎨", text: "Bringing creative visions to life", scores: { creative_score: 3, visual_score: 1, audio_score: 1 } },
                    { emoji: "❤️", text: "Creating experiences that emotionally impact people", scores: { narrative_score: 3, user_focus: 2 } },
                    { emoji: "📈", text: "Building products that reach millions", scores: { business_score: 3, social_score: 2 } },
                    { emoji: "👑", text: "Leading teams and shaping company direction", scores: { leadership_preference: 3, business_score: 2 } }
                ]
            },
            {
                question: "How do you approach learning new skills?",
                maxSelections: 2,
                options: [
                    { emoji: "📚", text: "Deep study of documentation and technical resources", scores: { technical_score: 3, detail_orientation: 2 } },
                    { emoji: "🎨", text: "Hands-on experimentation and creative exploration", scores: { creative_score: 2, visual_score: 1, audio_score: 1, innovation_drive: 2 } },
                    { emoji: "👨‍🏫", text: "Learning from mentors and peer collaboration", scores: { social_score: 3, collaboration_style: 3 } },
                    { emoji: "📊", text: "Analyzing case studies and industry examples", scores: { business_score: 2, narrative_score: 1, systems_score: 1 } },
                    { emoji: "🔨", text: "Building projects and learning through iteration", scores: { technical_score: 2, systems_score: 2, innovation_drive: 1 } }
                ]
            },
            {
                question: "What games do you find yourself drawn to?",
                maxSelections: 2,
                options: [
                    { emoji: "🎮", text: "Technical showcases (realistic graphics, complex systems)", scores: { technical_score: 2, visual_score: 2, detail_orientation: 1 } },
                    { emoji: "🎨", text: "Artistic indies (unique visual style, creative gameplay)", scores: { creative_score: 3, visual_score: 2, innovation_drive: 2 } },
                    { emoji: "📖", text: "Story-rich RPGs (deep narrative, character development)", scores: { narrative_score: 3, creative_score: 1, user_focus: 1 } },
                    { emoji: "🎵", text: "Music/rhythm games (audio-focused, precision-based)", scores: { audio_score: 3, detail_orientation: 2 } },
                    { emoji: "👥", text: "Multiplayer competitive (community, balance, esports)", scores: { social_score: 2, systems_score: 2, business_score: 1 } },
                    { emoji: "📱", text: "Mobile/casual (accessible, broad appeal)", scores: { business_score: 2, user_focus: 2, systems_score: 1 } }
                ]
            },

            {
                question: "What type of game development environment appeals to you most?",
                maxSelections: 1,
                options: [
                    { emoji: "🏢", text: "Large AAA studio with established processes", scores: { business_score: 3, collaboration_style: 2, systems_score: 1 } },
                    { emoji: "🚀", text: "Growing indie studio with creative freedom", scores: { creative_score: 3, innovation_drive: 2, collaboration_style: 1 } },
                    { emoji: "🏠", text: "Remote-first company with flexible work", scores: { detail_orientation: 2, collaboration_style: 1, innovation_drive: 1 } },
                    { emoji: "🎯", text: "Startup with cutting-edge technology", scores: { technical_score: 2, innovation_drive: 3, business_score: 1 } },
                    { emoji: "🎮", text: "Any environment where I can make great games", scores: { user_focus: 3, collaboration_style: 2 } }
                ]
            },
            {
                question: "Choose your workflow preferences",
                type: "picture",
                maxSelections: 2,
                instructions: "Choose 1-2 workflow styles that resonate with you",
                options: [
                    { emoji: "🏃‍♂️", text: "Sprint-based with quick iterations", scores: { technical_score: 2, business_score: 2, collaboration_style: 1 } },
                    { emoji: "🎨", text: "Creative exploration with flexible timelines", scores: { creative_score: 3, visual_score: 1, audio_score: 1, innovation_drive: 2 } },
                    { emoji: "📋", text: "Structured phases with clear documentation", scores: { business_score: 2, technical_score: 1, detail_orientation: 3 } },
                    { emoji: "🔄", text: "Continuous feedback and user testing cycles", scores: { systems_score: 2, user_focus: 3, social_score: 1 } },
                    { emoji: "🚀", text: "Rapid prototyping and experimentation", scores: { innovation_drive: 3, technical_score: 2, creative_score: 1 } }
                ]
            },
            {
                question: "What platform or technology excites you most for game development?",
                maxSelections: 2,
                options: [
                    { emoji: "🖥️", text: "PC gaming with cutting-edge graphics and performance", scores: { technical_score: 3, visual_score: 2, detail_orientation: 1 } },
                    { emoji: "🎮", text: "Console development with optimized, polished experiences", scores: { systems_score: 2, technical_score: 2, business_score: 1 } },
                    { emoji: "📱", text: "Mobile games reaching billions of players worldwide", scores: { business_score: 3, user_focus: 2, systems_score: 1 } },
                    { emoji: "🥽", text: "VR/AR creating entirely new types of experiences", scores: { innovation_drive: 3, creative_score: 2, technical_score: 1 } },
                    { emoji: "🌐", text: "Web games accessible to anyone with a browser", scores: { user_focus: 2, technical_score: 1, innovation_drive: 2 } },
                    { emoji: "🎯", text: "Cross-platform games that work everywhere", scores: { technical_score: 2, systems_score: 3, business_score: 1 } }
                ]
            },
            {
                question: "Rank your top 2 career motivators",
                type: "ranking",
                maxSelections: 2,
                instructions: "Drag to rank your top 2 career motivators",
                options: [
                    {
                        emoji: "🧩",
                        text: "Solving complex challenges that others can't",
                        scores: {
                            rank1: { technical_score: 3, innovation_drive: 2 },
                            rank2: { technical_score: 2, detail_orientation: 1 }
                        }
                    },
                    {
                        emoji: "🎨",
                        text: "Bringing creative visions to life",
                        scores: {
                            rank1: { creative_score: 3, visual_score: 1, audio_score: 1 },
                            rank2: { creative_score: 2, innovation_drive: 1 }
                        }
                    },
                    {
                        emoji: "❤️",
                        text: "Creating experiences that emotionally impact people",
                        scores: {
                            rank1: { narrative_score: 3, user_focus: 2 },
                            rank2: { user_focus: 2, creative_score: 1 }
                        }
                    },
                    {
                        emoji: "📈",
                        text: "Building products that reach millions",
                        scores: {
                            rank1: { business_score: 3, social_score: 2 },
                            rank2: { business_score: 2, systems_score: 1 }
                        }
                    },
                    {
                        emoji: "👑",
                        text: "Leading teams and shaping company direction",
                        scores: {
                            rank1: { leadership_preference: 3, business_score: 2 },
                            rank2: { leadership_preference: 2, social_score: 1 }
                        }
                    }
                ]
            },
            {
                question: "What aspect of game development do you find most rewarding?",
                maxSelections: 2,
                options: [
                    { emoji: "⚡", text: "Solving complex technical challenges that seemed impossible", scores: { technical_score: 3, innovation_drive: 2, detail_orientation: 1 } },
                    { emoji: "🎨", text: "Seeing your creative vision come to life", scores: { creative_score: 3, visual_score: 2, audio_score: 1 } },
                    { emoji: "😊", text: "Watching players enjoy and connect with your work", scores: { user_focus: 3, narrative_score: 2, social_score: 1 } },
                    { emoji: "🚀", text: "Shipping a polished product that reaches many people", scores: { business_score: 3, systems_score: 2, collaboration_style: 1 } },
                    { emoji: "🌱", text: "Mentoring others and building great teams", scores: { leadership_preference: 3, social_score: 2, collaboration_style: 2 } },
                    { emoji: "🔬", text: "Pushing the boundaries of what's technically possible", scores: { innovation_drive: 3, technical_score: 2, systems_score: 1 } }
                ]
            },
            {
                question: "Rate how much you enjoy these activities",
                type: "rating",
                instructions: "Rate from 1 (Dislike) to 5 (Love it)",
                options: [
                    { emoji: "👥", text: "Managing people and their career development", scores: { leadership_preference: 0.8, social_score: 0.4 } },
                    { emoji: "⏰", text: "Working under tight deadlines and pressure", scores: { business_score: 0.4, detail_orientation: 0.3 } },
                    { emoji: "🎨", text: "Presenting creative work for critique and feedback", scores: { creative_score: 0.4, visual_score: 0.3, social_score: 0.2 } },
                    { emoji: "🔧", text: "Debugging complex technical issues", scores: { technical_score: 0.8, detail_orientation: 0.4 } },
                    { emoji: "💬", text: "Communicating with external stakeholders", scores: { business_score: 0.6, social_score: 0.4 } }
                ]
            },
            {
                question: "What games do you find yourself drawn to?",
                type: "picture",
                maxSelections: 2,
                instructions: "Select 1-2 game types that you're most drawn to",
                options: [
                    { emoji: "🎮", text: "Technical showcases (realistic graphics, complex systems)", scores: { technical_score: 2, visual_score: 2, detail_orientation: 1 } },
                    { emoji: "🎨", text: "Artistic indies (unique visual style, creative gameplay)", scores: { creative_score: 3, visual_score: 2, innovation_drive: 2 } },
                    { emoji: "📖", text: "Story-rich RPGs (deep narrative, character development)", scores: { narrative_score: 3, creative_score: 1, user_focus: 1 } },
                    { emoji: "🎵", text: "Music/rhythm games (audio-focused, precision-based)", scores: { audio_score: 3, detail_orientation: 2 } },
                    { emoji: "👥", text: "Multiplayer competitive (community, balance, esports)", scores: { social_score: 2, systems_score: 2, business_score: 1 } },
                    { emoji: "📱", text: "Mobile/casual (accessible, broad appeal)", scores: { business_score: 2, user_focus: 2, systems_score: 1 } }
                ]
            },
            {
                question: "Final priority check - Rank ALL from most important (1) to least important (6)",
                type: "ranking",
                maxSelections: 6,
                instructions: "Drag to rank ALL of these from most important (1) to least important (6) for your ideal role",
                options: [
                    {
                        emoji: "🎯",
                        text: "Creative freedom and artistic expression",
                        scores: {
                            rank1: { creative_score: 2 }, rank2: { creative_score: 2 },
                            rank3: { creative_score: 1 }, rank4: { creative_score: 1 },
                            rank5: {}, rank6: {}
                        }
                    },
                    {
                        emoji: "💰",
                        text: "Financial success and career growth",
                        scores: {
                            rank1: { business_score: 2 }, rank2: { business_score: 2 },
                            rank3: { business_score: 1 }, rank4: { business_score: 1 },
                            rank5: {}, rank6: {}
                        }
                    },
                    {
                        emoji: "🔧",
                        text: "Technical excellence and innovation",
                        scores: {
                            rank1: { technical_score: 2 }, rank2: { technical_score: 2 },
                            rank3: { technical_score: 1 }, rank4: { technical_score: 1 },
                            rank5: {}, rank6: {}
                        }
                    },
                    {
                        emoji: "❤️",
                        text: "Meaningful impact on players",
                        scores: {
                            rank1: { user_focus: 2, narrative_score: 1 }, rank2: { user_focus: 2, narrative_score: 1 },
                            rank3: { user_focus: 1 }, rank4: { user_focus: 1 },
                            rank5: {}, rank6: {}
                        }
                    },
                    {
                        emoji: "🤝",
                        text: "Collaborative team environment",
                        scores: {
                            rank1: { social_score: 2, collaboration_style: 2 }, rank2: { social_score: 2, collaboration_style: 2 },
                            rank3: { social_score: 1 }, rank4: { social_score: 1 },
                            rank5: {}, rank6: {}
                        }
                    },
                    {
                        emoji: "👑",
                        text: "Leadership and decision-making authority",
                        scores: {
                            rank1: { leadership_preference: 2, business_score: 1 }, rank2: { leadership_preference: 2, business_score: 1 },
                            rank3: { leadership_preference: 1 }, rank4: { leadership_preference: 1 },
                            rank5: {}, rank6: {}
                        }
                    }
                ]
            }
        ];

        let scores = {
            creative_score: 0,
            technical_score: 0,
            visual_score: 0,
            audio_score: 0,
            social_score: 0,
            business_score: 0,
            narrative_score: 0,
            systems_score: 0,
            leadership_preference: 0,
            collaboration_style: 0,
            detail_orientation: 0,
            innovation_drive: 0,
            user_focus: 0
        };

        let currentQuestion = 0;
        let selectedAnswers = [];

        // Generate enhanced book recommendation section
        function generateBookSection(bookTitle) {
            const book = bookDatabase[bookTitle];
            if (!book) return '';

            const formatDisplayNames = {
                'paperback': 'Paperback',
                'hardcover': 'Hardcover',
                'kindle': 'Kindle',
                'audiobook': 'Audiobook'
            };

            const formatIcons = {
                'paperback': '📖',
                'hardcover': '📚',
                'kindle': '📱',
                'audiobook': '🎧'
            };

            let purchaseLinks = '';
            for (const [format, url] of Object.entries(book.amazonLinks)) {
                const formatName = formatDisplayNames[format] || format;
                const icon = formatIcons[format] || '📄';
                purchaseLinks += `
                    <a href="${url}"
                       target="_blank"
                       rel="noopener"
                       class="purchase-link ${format}-link">
                        ${icon} ${formatName}
                    </a>
                `;
            }

            return `
                <div class="book-recommendation">
                    <div class="book-header">
                        <div class="book-cover">
                            <img src="https://images-na.ssl-images-amazon.com/images/P/${book.amazonASIN}.01.L.jpg"
                                 alt="${bookTitle} cover"
                                 loading="lazy"
                                 onerror="this.style.display='none'">
                        </div>
                        <div class="book-info">
                            <h4 class="book-title">"${bookTitle}"</h4>
                            <p class="book-author">by ${book.author}</p>
                            <div class="book-meta">
                                <span class="book-time">${book.timeToRead}</span>
                                <span class="book-difficulty">${book.difficulty}</span>
                            </div>
                        </div>
                    </div>

                    <div class="book-description">
                        <p><strong>Why this book is essential:</strong> ${book.whyEssential}</p>
                    </div>

                    <div class="book-takeaways">
                        <h5>Key Takeaways:</h5>
                        <ul>
                            ${book.keyTakeaways.map(takeaway => `<li>${takeaway}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="book-purchase">
                        <div class="purchase-options">
                            ${purchaseLinks}
                        </div>
                        ${book.freeVersion ? `
                            <div class="free-option">
                                <p><strong>💡 Free Option:</strong> ${book.freeVersion}</p>
                            </div>
                        ` : ''}
                    </div>

                    <div class="affiliate-disclosure">
                        <small>📚 As an Amazon Associate, I earn from qualifying purchases. This helps support the creation of more career resources like this quiz!</small>
                    </div>
                </div>
            `;
        }

        function selectOption(optionIndex) {
            console.log('Option selected:', optionIndex);
            const question = quizData[currentQuestion];
            const optionElement = document.querySelector(`[data-option="${optionIndex}"]`);
            
            if (!selectedAnswers[currentQuestion]) {
                selectedAnswers[currentQuestion] = [];
            }
            
            const currentSelections = selectedAnswers[currentQuestion];
            const isSelected = currentSelections.includes(optionIndex);
            
            if (isSelected) {
                // Deselect
                selectedAnswers[currentQuestion] = currentSelections.filter(i => i !== optionIndex);
                optionElement.classList.remove('selected');
            } else {
                // Select (if under limit)
                if (currentSelections.length < question.maxSelections) {
                    selectedAnswers[currentQuestion].push(optionIndex);
                    optionElement.classList.add('selected');
                }
            }
            
            updateNavigation();
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const question = quizData[currentQuestion];

            let hasSelections = false;

            if (question.type === 'ranking') {
                // For ranking questions, check if we have the required number of rankings
                const rankings = selectedAnswers[currentQuestion];
                hasSelections = rankings && rankings.length >= question.maxSelections;
            } else if (question.type === 'rating') {
                // For rating questions, always valid since they have default values
                hasSelections = true;
            } else if (question.type === 'picture') {
                // For picture questions, check if we have any selections
                hasSelections = selectedAnswers[currentQuestion] && selectedAnswers[currentQuestion].length > 0;
            } else {
                // For regular questions, check if we have any selections
                hasSelections = selectedAnswers[currentQuestion] && selectedAnswers[currentQuestion].length > 0;
            }

            // Show/hide previous button
            prevBtn.style.display = currentQuestion > 0 ? 'block' : 'none';

            // Enable/disable next button
            nextBtn.disabled = !hasSelections;

            // Update next button text
            if (currentQuestion === quizData.length - 1) {
                nextBtn.textContent = 'Get Results';
            } else {
                nextBtn.textContent = 'Next';
            }
        }

        function previousQuestion() {
            if (currentQuestion > 0) {
                currentQuestion--;
                showQuestion(currentQuestion);
                updateProgress();
            }
        }

        function nextQuestion() {
            if (currentQuestion < quizData.length - 1) {
                currentQuestion++;
                showQuestion(currentQuestion);
                updateProgress();
            } else {
                showResults();
            }
        }

        function showQuestion(questionIndex) {
            const question = quizData[questionIndex];
            const quizContent = document.getElementById('quizContent');

            if (question.type === 'ranking') {
                showRankingQuestion(question, questionIndex);
            } else if (question.type === 'rating') {
                showRatingQuestion(question, questionIndex);
            } else if (question.type === 'picture') {
                showPictureQuestion(question, questionIndex);
            } else {
                // Regular multiple choice question
                quizContent.innerHTML = `
                    <div class="question-container">
                        <div class="question-type">Choose up to ${question.maxSelections} option${question.maxSelections > 1 ? 's' : ''}</div>
                        <h2 class="question-title">${question.question}</h2>
                        <div class="options-container">
                            ${question.options.map((option, index) => `
                                <div class="option" onclick="selectOption(${index})" data-option="${index}">
                                    <span class="option-emoji">${option.emoji}</span>
                                    <span class="option-text">${option.text}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;

                // Restore previous selections
                if (selectedAnswers[questionIndex]) {
                    selectedAnswers[questionIndex].forEach(optionIndex => {
                        const element = document.querySelector(`[data-option="${optionIndex}"]`);
                        if (element) {
                            element.classList.add('selected');
                        }
                    });
                }
            }

            updateNavigation();
            document.getElementById('questionCounter').textContent = `Question ${questionIndex + 1} of ${quizData.length}`;
        }

        function updateProgress() {
            const progress = ((currentQuestion + 1) / quizData.length) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        function showRankingQuestion(question, questionIndex) {
            const quizContent = document.getElementById('quizContent');

            quizContent.innerHTML = `
                <div class="question-container">
                    <div class="question-type">${question.instructions}</div>
                    <h2 class="question-title">${question.question}</h2>
                    <div class="ranking-container">
                        <div class="ranking-source">
                            <h3>Available Options</h3>
                            <div id="source-items">
                                ${question.options.map((option, index) => `
                                    <div class="ranking-item" draggable="true" data-option="${index}">
                                        <span class="ranking-emoji">${option.emoji}</span>
                                        <span class="ranking-text">${option.text}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="ranking-target">
                            <h3>Your Top ${question.maxSelections} (Drag Here)</h3>
                            <div id="target-items">
                                ${Array.from({length: question.maxSelections}, (_, i) => `
                                    <div class="drop-zone" data-rank="${i + 1}">
                                        <span>${i + 1}. Drop item here</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Initialize drag and drop
            initializeDragAndDrop(questionIndex);

            // Restore previous rankings if they exist
            if (selectedAnswers[questionIndex]) {
                restoreRankings(selectedAnswers[questionIndex], questionIndex);
            }
        }

        function initializeDragAndDrop(questionIndex) {
            const sourceItems = document.querySelectorAll('#source-items .ranking-item');
            const dropZones = document.querySelectorAll('.drop-zone');

            sourceItems.forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
                item.addEventListener('dragend', handleDragEnd);
            });

            dropZones.forEach(zone => {
                zone.addEventListener('dragover', handleDragOver);
                zone.addEventListener('drop', handleDrop);
                zone.addEventListener('dragenter', handleDragEnter);
                zone.addEventListener('dragleave', handleDragLeave);
            });
        }

        let draggedElement = null;

        function handleDragStart(e) {
            draggedElement = e.target;
            e.target.classList.add('dragging');
        }

        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
            draggedElement = null;
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDragEnter(e) {
            e.preventDefault();
            if (e.target.classList.contains('drop-zone') && !e.target.classList.contains('filled')) {
                e.target.classList.add('drag-over');
            }
        }

        function handleDragLeave(e) {
            if (e.target.classList.contains('drop-zone')) {
                e.target.classList.remove('drag-over');
            }
        }

        function handleDrop(e) {
            e.preventDefault();
            const dropZone = e.target.closest('.drop-zone');

            if (dropZone && !dropZone.classList.contains('filled') && draggedElement) {
                // Remove from source
                draggedElement.remove();

                // Add to drop zone
                const rank = dropZone.dataset.rank;
                const optionIndex = draggedElement.dataset.option;

                dropZone.innerHTML = `
                    <div class="ranking-item ranked" data-option="${optionIndex}">
                        <span class="ranking-position">${rank}</span>
                        <span class="ranking-emoji">${draggedElement.querySelector('.ranking-emoji').textContent}</span>
                        <span class="ranking-text">${draggedElement.querySelector('.ranking-text').textContent}</span>
                    </div>
                `;
                dropZone.classList.add('filled');
                dropZone.classList.remove('drag-over');

                // Add click to remove functionality
                dropZone.querySelector('.ranking-item').addEventListener('click', function() {
                    removeFromRanking(this, optionIndex);
                });

                updateRankingAnswers();
            }
        }

        function removeFromRanking(item, optionIndex) {
            const dropZone = item.closest('.drop-zone');
            const question = quizData[currentQuestion];
            const option = question.options[optionIndex];

            // Remove from drop zone
            dropZone.innerHTML = `<span>${dropZone.dataset.rank}. Drop item here</span>`;
            dropZone.classList.remove('filled');

            // Add back to source
            const sourceContainer = document.getElementById('source-items');
            const newItem = document.createElement('div');
            newItem.className = 'ranking-item';
            newItem.draggable = true;
            newItem.dataset.option = optionIndex;
            newItem.innerHTML = `
                <span class="ranking-emoji">${option.emoji}</span>
                <span class="ranking-text">${option.text}</span>
            `;

            newItem.addEventListener('dragstart', handleDragStart);
            newItem.addEventListener('dragend', handleDragEnd);

            sourceContainer.appendChild(newItem);

            updateRankingAnswers();
        }

        function updateRankingAnswers() {
            const filledZones = document.querySelectorAll('.drop-zone.filled');
            const rankings = [];

            filledZones.forEach(zone => {
                const rank = parseInt(zone.dataset.rank);
                const optionIndex = parseInt(zone.querySelector('.ranking-item').dataset.option);
                rankings.push({ rank, optionIndex });
            });

            // Sort by rank and store
            rankings.sort((a, b) => a.rank - b.rank);
            selectedAnswers[currentQuestion] = rankings;

            updateNavigation();
        }

        function restoreRankings(rankings, questionIndex) {
            // This would restore previous rankings - implement if needed for back navigation
        }

        function showRatingQuestion(question, questionIndex) {
            const quizContent = document.getElementById('quizContent');

            quizContent.innerHTML = `
                <div class="question-container">
                    <div class="question-type">${question.instructions}</div>
                    <h2 class="question-title">${question.question}</h2>
                    <div class="rating-container">
                        ${question.options.map((option, index) => `
                            <div class="rating-item">
                                <div class="rating-label">
                                    <span class="rating-emoji">${option.emoji}</span>
                                    <span class="rating-text">${option.text}</span>
                                </div>
                                <div class="rating-scale">
                                    <span class="rating-labels">1</span>
                                    <input type="range" min="1" max="5" value="3"
                                           data-option="${index}"
                                           oninput="updateRating(${index}, this.value, ${questionIndex})">
                                    <span class="rating-labels">5</span>
                                    <span class="rating-value" id="rating-value-${index}">3</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            // Initialize ratings
            if (!selectedAnswers[questionIndex]) {
                selectedAnswers[questionIndex] = question.options.map(() => 3); // Default to 3
            }

            // Restore previous ratings
            selectedAnswers[questionIndex].forEach((rating, index) => {
                const slider = document.querySelector(`input[data-option="${index}"]`);
                const valueDisplay = document.getElementById(`rating-value-${index}`);
                if (slider && valueDisplay) {
                    slider.value = rating;
                    valueDisplay.textContent = rating;
                }
            });
        }

        function updateRating(optionIndex, value, questionIndex) {
            document.getElementById(`rating-value-${optionIndex}`).textContent = value;

            if (!selectedAnswers[questionIndex]) {
                selectedAnswers[questionIndex] = [];
            }
            selectedAnswers[questionIndex][optionIndex] = parseInt(value);

            updateNavigation();
        }

        function showPictureQuestion(question, questionIndex) {
            const quizContent = document.getElementById('quizContent');

            quizContent.innerHTML = `
                <div class="question-container">
                    <div class="question-type">${question.instructions}</div>
                    <h2 class="question-title">${question.question}</h2>
                    <div class="picture-container">
                        ${question.options.map((option, index) => `
                            <div class="picture-option" onclick="selectPictureOption(${index}, ${questionIndex})" data-option="${index}">
                                <span class="picture-emoji">${option.emoji}</span>
                                <span class="picture-text">${option.text}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            // Restore previous selections
            if (selectedAnswers[questionIndex]) {
                selectedAnswers[questionIndex].forEach(optionIndex => {
                    const element = document.querySelector(`[data-option="${optionIndex}"]`);
                    if (element) {
                        element.classList.add('selected');
                    }
                });
            }
        }

        function selectPictureOption(optionIndex, questionIndex) {
            const question = quizData[questionIndex];
            const optionElement = document.querySelector(`[data-option="${optionIndex}"]`);

            if (!selectedAnswers[questionIndex]) {
                selectedAnswers[questionIndex] = [];
            }

            const currentSelections = selectedAnswers[questionIndex];
            const isSelected = currentSelections.includes(optionIndex);

            if (isSelected) {
                // Deselect
                selectedAnswers[questionIndex] = currentSelections.filter(i => i !== optionIndex);
                optionElement.classList.remove('selected');
            } else {
                // Select (if under limit)
                if (currentSelections.length < question.maxSelections) {
                    selectedAnswers[questionIndex].push(optionIndex);
                    optionElement.classList.add('selected');
                }
            }

            updateNavigation();
        }

        function showResults() {
            // Calculate scores
            selectedAnswers.forEach((selections, questionIndex) => {
                const question = quizData[questionIndex];

                if (selections) {
                    if (question.type === 'ranking') {
                        // Handle ranking questions
                        selections.forEach(ranking => {
                            const option = question.options[ranking.optionIndex];
                            const rankKey = `rank${ranking.rank}`;

                            if (option.scores[rankKey]) {
                                Object.keys(option.scores[rankKey]).forEach(scoreKey => {
                                    scores[scoreKey] += option.scores[rankKey][scoreKey];
                                });
                            }
                        });
                    } else if (question.type === 'rating') {
                        // Handle rating questions
                        selections.forEach((rating, optionIndex) => {
                            const option = question.options[optionIndex];
                            Object.keys(option.scores).forEach(scoreKey => {
                                scores[scoreKey] += option.scores[scoreKey] * rating;
                            });
                        });
                    } else {
                        // Handle regular multiple choice and picture questions
                        selections.forEach(optionIndex => {
                            const option = question.options[optionIndex];
                            Object.keys(option.scores).forEach(scoreKey => {
                                scores[scoreKey] += option.scores[scoreKey];
                            });
                        });
                    }
                }
            });
            
            // Determine role with enhanced logic
            const coreCategories = ['creative_score', 'technical_score', 'visual_score', 'audio_score', 'social_score', 'business_score', 'narrative_score', 'systems_score'];
            const topCategories = Object.entries(scores)
                .filter(([key]) => coreCategories.includes(key))
                .sort(([,a], [,b]) => b - a);

            const primaryCategory = topCategories[0][0];
            const primaryScore = topCategories[0][1];

            let role = "Game Developer";
            let description = "You have a balanced approach to game development with diverse interests and skills.";

            // Enhanced role determination logic with destiny-like descriptions
            let strengths = [];
            let weaknesses = [];
            let bookRecommendation = "";
            let actionableAdvice = "";
            let first30Days = [];
            let portfolioEssentials = [];
            let fundamentalSkills = [];
            let salaryRange = "";
            let careerProgression = "";

            if (primaryCategory === 'technical_score') {
                if (scores.systems_score >= 8 && scores.leadership_preference >= 6) {
                    role = "Technical Director";
                    description = "You're the rare breed who can architect complex systems while keeping teams aligned and productive. Like the lead engineer on a AAA engine team, you see the big picture and the implementation details simultaneously. You're the person others turn to when the technical challenges seem impossible.";
                    strengths = ["Exceptional systems thinking", "Natural technical leadership", "Ability to translate complex concepts", "Strategic problem-solving"];
                    weaknesses = ["May become impatient with slower learners", "Risk of over-engineering solutions", "Tendency to focus on perfection over delivery"];
                    bookRecommendation = "The Mythical Man-Month by Frederick Brooks";
                    actionableAdvice = "Develop your mentoring skills - your greatest impact will come through elevating entire teams. Practice explaining complex concepts in simple terms, and remember that sometimes 'good enough' solutions ship while perfect ones don't.";

                    first30Days = [
                        "Week 1: Download Unity (free) and follow their 'Create with Code' tutorial. Take screenshots of each step - you'll use these later!",
                        "Week 2: Try to recreate Pong from scratch using what you learned. Document what was hard vs easy (save this writeup!)",
                        "Week 3: Find someone else's Pong game online and see how they solved the same problems differently. Write down 3 things they did better",
                        "Week 4: Create a simple portfolio webpage showing your Pong game, screenshots from Week 1, and your learning notes. This is your first portfolio piece!"
                    ];

                    portfolioEssentials = [
                        "Custom engine component (physics, rendering, or audio system) with full source code",
                        "Performance optimization case study with before/after metrics and methodology",
                        "Technical design document for a complex system (multiplayer, procedural generation, etc.)",
                        "Code review examples showing mentorship and technical communication skills",
                        "Architecture diagram of a scalable game system with detailed explanations"
                    ];

                    fundamentalSkills = [
                        "Master C++ memory management and performance optimization",
                        "Understand graphics programming pipeline (vertex/fragment shaders)",
                        "Learn multithreading and concurrent programming patterns",
                        "Study data structures and algorithms specific to game development",
                        "Practice technical writing and documentation skills"
                    ];

                    salaryRange = "Entry: Not typically entry-level (requires 8+ years) | Mid: $140,000 - $180,000 | Senior: $180,000 - $250,000+";
                    careerProgression = "Usually promoted from Senior Programmer roles. Requires proven leadership and shipped games.";
                } else if (scores.innovation_drive >= 8) {
                    role = "Tools Programmer";
                    description = "You're the developer who builds the tools that make other developers' lives better. Think of the people who created Unity's editor, Unreal's Blueprint system, or Maya's scripting tools. You see workflow inefficiencies as boss fights to be conquered, and your solutions multiply entire teams' productivity.";
                    strengths = ["Innovative problem-solving", "Workflow optimization mindset", "Empathy for developer pain points", "Automation expertise"];
                    weaknesses = ["May over-optimize prematurely", "Risk of building tools nobody asked for", "Tendency to reinvent existing solutions"];
                    bookRecommendation = "The Pragmatic Programmer by David Thomas & Andrew Hunt";
                    actionableAdvice = "Always validate tool ideas with actual users before building. Your superpower is making others more productive - focus on solving real pain points, not theoretical ones. Build MVPs and iterate based on feedback.";

                    first30Days = [
                        "Week 1: Ask 3 friends/family what repetitive tasks annoy them on their computer. Write down their answers and take notes on their frustrations",
                        "Week 2: Learn basic Python with a free online tutorial (try Python.org's beginner guide). Practice by writing a simple script that renames files automatically",
                        "Week 3: Download Blender (free) and spend time just clicking around the interface. Notice what's confusing vs intuitive - write this down!",
                        "Week 4: Use your Python knowledge to create a simple tool that solves one of the problems from Week 1. Show it to the person who had that problem!"
                    ];

                    portfolioEssentials = [
                        "Custom editor tool for Unity/Unreal with intuitive UI",
                        "Automation script that saves hours of manual work (with time savings documented)",
                        "Asset pipeline tool that converts/optimizes files automatically",
                        "Debug visualization tool that makes complex data easy to understand",
                        "Before/after workflow comparison showing productivity improvements"
                    ];

                    fundamentalSkills = [
                        "Learn GUI programming (ImGui, Qt, or web-based tools)",
                        "Master scripting languages (Python, C#, JavaScript)",
                        "Understand file formats and data serialization",
                        "Study user experience design principles",
                        "Practice gathering and implementing user feedback"
                    ];

                    salaryRange = "Entry: $75,000 - $95,000 | Mid: $95,000 - $130,000 | Senior: $130,000 - $170,000";
                    careerProgression = "High demand role. Often hired at mid-level with general programming experience.";
                } else if (scores.systems_score >= 6) {
                    role = "Engine Programmer";
                    description = "You're drawn to the core systems that make games possible - the rendering pipeline, physics simulation, memory management. Like the engineers behind id Tech, Unreal Engine, or CryEngine, you find satisfaction in solving the hardest technical problems that enable everyone else's creativity.";
                    strengths = ["Performance optimization expertise", "Low-level system understanding", "Mathematical problem-solving", "Attention to technical detail"];
                    weaknesses = ["May prioritize performance over readability", "Risk of premature optimization", "Tendency to work in isolation"];
                    bookRecommendation = "Game Engine Architecture by Jason Gregory";
                    actionableAdvice = "Balance your optimization instincts with practical deadlines. Document your complex systems thoroughly - future you (and your teammates) will thank you. Consider the maintainability cost of every optimization.";
                } else {
                    role = "Gameplay Programmer";
                    description = "You're the programmer who makes games fun. You implement the mechanics that players interact with directly - combat systems, character controllers, UI responsiveness. Your code is the bridge between design documents and player joy. You understand that clean code matters, but player experience matters more.";
                    strengths = ["Game mechanics intuition", "Player experience focus", "Rapid prototyping ability", "Creative problem-solving"];
                    weaknesses = ["May sacrifice code quality for quick iteration", "Risk of feature creep", "Tendency to over-complicate simple mechanics"];
                    bookRecommendation = "Game Programming Patterns by Robert Nystrom";
                    actionableAdvice = "Embrace rapid prototyping but refactor ruthlessly. Your strength lies in making ideas playable quickly - use this to validate concepts early. Always playtest your implementations yourself.";

                    first30Days = [
                        "Week 1: Download Unity and follow their '2D Platformer' tutorial. Play the finished game and record a short video of yourself playing it",
                        "Week 2: Try changing the jump height and speed in your platformer. Notice how it feels different. Write down what feels good vs bad",
                        "Week 3: Add a simple sound effect when the character jumps (Unity has free sounds). See how much better it feels with audio!",
                        "Week 4: Create a 1-minute video showing your modified game, explaining what you changed and why. Post it online - this is your first game dev content!"
                    ];

                    portfolioEssentials = [
                        "Character controller that feels responsive and polished",
                        "Combat system with clear feedback and satisfying impact",
                        "UI system that's intuitive and accessible",
                        "Game mechanic that's easy to learn but hard to master",
                        "Prototype that demonstrates 'game feel' and player feedback"
                    ];

                    fundamentalSkills = [
                        "Master state machines and behavior trees",
                        "Learn input handling and player feedback systems",
                        "Understand game loops and frame-rate independence",
                        "Study game design patterns (Observer, Command, State)",
                        "Practice rapid prototyping and iteration"
                    ];

                    salaryRange = "Entry: $70,000 - $90,000 | Mid: $90,000 - $120,000 | Senior: $120,000 - $160,000";
                    careerProgression = "Most common entry point for programming roles. Good work-life balance.";
                }
            } else if (primaryCategory === 'creative_score') {
                if (scores.visual_score >= 8) {
                    role = "Art Director";
                    description = "You're the visual architect who defines how games look and feel. Like the art directors behind Journey, Ori and the Blind Forest, or Cuphead, you shape entire aesthetic experiences. You understand that art isn't just decoration - it's communication, emotion, and world-building all rolled into one.";
                    strengths = ["Strong aesthetic vision", "Team leadership in creative contexts", "Visual storytelling ability", "Style consistency maintenance"];
                    weaknesses = ["May be overly critical of artistic work", "Risk of perfectionism blocking progress", "Tendency to micromanage creative decisions"];
                    bookRecommendation = "The Visual Story by Bruce Block";
                    actionableAdvice = "Learn to communicate your vision through mood boards and style guides rather than just verbal direction. Trust your team's creativity while maintaining overall coherence. Set clear artistic boundaries but allow exploration within them.";
                } else if (scores.narrative_score >= 8) {
                    role = "Creative Director";
                    description = "You're the creative mastermind who ensures all elements serve a unified vision. Think of directors like Amy Hennig (Uncharted), Ken Levine (BioShock), or Hideo Kojima (Metal Gear). You see how gameplay, story, art, and sound weave together to create experiences that players remember for years.";
                    strengths = ["Holistic creative vision", "Narrative structure expertise", "Cross-disciplinary thinking", "Emotional design intuition"];
                    weaknesses = ["May struggle with technical constraints", "Risk of over-ambitious scope", "Tendency to change direction frequently"];
                    bookRecommendation = "The Art of Computer Game Design by Chris Crawford";
                    actionableAdvice = "Ground your creative vision in player psychology and technical reality. Create clear creative pillars that guide all decisions. Learn to say 'no' to good ideas that don't serve the core vision.";
                } else if (scores.leadership_preference >= 6) {
                    role = "Lead Game Designer";
                    description = "You're the design leader who coordinates systems and guides teams toward cohesive gameplay. Like the lead designers on games like Civilization, XCOM, or Overwatch, you balance creative vision with practical implementation, ensuring all systems work together harmoniously.";
                    strengths = ["Systems design thinking", "Team coordination skills", "Player psychology understanding", "Design communication ability"];
                    weaknesses = ["May over-design simple features", "Risk of design-by-committee", "Tendency to second-guess decisions"];
                    bookRecommendation = "The Art of Computer Game Design by Chris Crawford";
                    actionableAdvice = "Develop strong playtesting instincts and trust them over theoretical design. Create clear design documents but be ready to iterate based on player feedback. Your role is to facilitate great design, not dictate it.";
                } else {
                    role = "Game Designer";
                    description = "You're the architect of fun - the person who crafts the rules, systems, and interactions that make games engaging. Whether designing levels like those in Portal, mechanics like those in Tetris, or progression systems like those in RPGs, you understand what makes players tick.";
                    strengths = ["Gameplay intuition", "Iterative design mindset", "Player empathy", "System balance understanding"];
                    weaknesses = ["May over-iterate without shipping", "Risk of designing for yourself vs. audience", "Tendency to add complexity unnecessarily"];
                    bookRecommendation = "The Art of Computer Game Design by Chris Crawford";
                    actionableAdvice = "Playtest early and often with diverse audiences. Learn to recognize when a design is 'good enough' to ship and can be improved post-launch. Study games outside your preferred genres for fresh perspectives.";

                    first30Days = [
                        "Week 1: Design a simple board game using just paper and basic pieces (like checkers). Write down the rules clearly and take photos of your setup",
                        "Week 2: Get 3 different people to play your board game. Watch them play and write down every time they get confused or have fun",
                        "Week 3: Improve your game based on what you learned. Change the rules to fix the confusing parts. Test it again with the same people",
                        "Week 4: Create a simple 'how to play' guide with your photos from Week 1 and your final rules. This shows your design process!"
                    ];

                    portfolioEssentials = [
                        "Original game mechanic with clear rules and engaging depth",
                        "Level design examples showing progression and pacing",
                        "Playtesting documentation with iteration examples",
                        "Game balance analysis with mathematical backing",
                        "Player psychology insights applied to design decisions"
                    ];

                    fundamentalSkills = [
                        "Master rapid prototyping techniques (paper, digital tools)",
                        "Learn to conduct effective playtesting sessions",
                        "Understand game balance and progression curves",
                        "Study player psychology and motivation theory",
                        "Practice clear design communication and documentation"
                    ];

                    salaryRange = "Entry: $55,000 - $75,000 | Mid: $75,000 - $100,000 | Senior: $100,000 - $140,000";
                    careerProgression = "Highly competitive entry-level. Portfolio and shipped games crucial.";
                }
            } else if (primaryCategory === 'visual_score') {
                if (scores.technical_score >= 6) {
                    role = "Technical Artist";
                    description = "You're the bridge between art and code - the person who makes impossible visual ideas actually work in-engine. Like the technical artists who figured out how to make Breath of the Wild's art style run on Switch, or how to create Overwatch's stylized lighting system. You solve problems that pure artists and pure programmers can't.";
                    strengths = ["Dual art/tech expertise", "Problem-solving creativity", "Pipeline optimization", "Cross-team communication"];
                    weaknesses = ["May be pulled in too many directions", "Risk of becoming a bottleneck", "Tendency to over-engineer artistic solutions"];
                    bookRecommendation = "Real-Time Rendering by Tomas Akenine-Möller";
                    actionableAdvice = "Specialize in one area (shaders, rigging, or tools) while maintaining broad knowledge. Create documentation and tutorials to scale your knowledge. Build strong relationships with both art and programming teams.";
                } else if (scores.leadership_preference >= 6) {
                    role = "Art Director";
                    description = "You're the visual architect who defines how games look and feel. Like the art directors behind Journey, Ori and the Blind Forest, or Cuphead, you shape entire aesthetic experiences. You understand that art isn't just decoration - it's communication, emotion, and world-building all rolled into one.";
                    strengths = ["Strong aesthetic vision", "Team leadership in creative contexts", "Visual storytelling ability", "Style consistency maintenance"];
                    weaknesses = ["May be overly critical of artistic work", "Risk of perfectionism blocking progress", "Tendency to micromanage creative decisions"];
                    bookRecommendation = "The Visual Story by Bruce Block";
                    actionableAdvice = "Learn to communicate your vision through mood boards and style guides rather than just verbal direction. Trust your team's creativity while maintaining overall coherence. Set clear artistic boundaries but allow exploration within them.";
                } else {
                    role = "Visual Artist";
                    description = "You're the artist who brings game worlds to life through concept art, 3D models, textures, or animations. Whether creating the atmospheric environments of Hollow Knight, the character designs of Overwatch, or the visual effects of Diablo, your work directly shapes what players see and feel.";
                    strengths = ["Strong artistic fundamentals", "Visual storytelling ability", "Attention to aesthetic detail", "Creative problem-solving"];
                    weaknesses = ["May struggle with technical constraints", "Risk of perfectionism over productivity", "Tendency to work in isolation"];
                    bookRecommendation = "Color and Light by James Gurney";
                    actionableAdvice = "Build a diverse portfolio showcasing different styles and technical skills. Learn the technical pipeline of your chosen specialty. Seek feedback early and often - art is communication, not just self-expression.";
                }
            } else if (primaryCategory === 'audio_score') {
                if (scores.technical_score >= 6) {
                    role = "Audio Programmer";
                    description = "You're the programmer who makes audio systems work seamlessly in games. Like the engineers behind Wwise, FMOD, or the spatial audio in games like Hellblade, you combine technical precision with audio knowledge to solve problems that pure programmers or pure audio designers can't handle alone.";
                    strengths = ["Audio system expertise", "Performance optimization", "Cross-platform audio knowledge", "Technical problem-solving"];
                    weaknesses = ["May over-optimize audio at expense of other systems", "Risk of building overly complex audio tools", "Tendency to work in technical isolation"];
                    bookRecommendation = "Designing Sound by Andy Farnell";
                    actionableAdvice = "Stay current with audio middleware and emerging technologies. Build strong relationships with audio designers to understand their workflow needs. Focus on tools that empower creators, not just technical achievements.";
                } else {
                    role = "Audio Designer";
                    description = "You're the sound architect who crafts the audio landscape of games. Whether creating the atmospheric soundscapes of Limbo, the dynamic music systems of Nier: Automata, or the satisfying audio feedback of Overwatch, you understand that great audio design is felt, not just heard.";
                    strengths = ["Musical and sound intuition", "Emotional design understanding", "Audio storytelling ability", "Technical audio skills"];
                    weaknesses = ["May over-design audio for simple interactions", "Risk of creating audio that fights gameplay", "Tendency to work without enough player feedback"];
                    bookRecommendation = "Audio for Games: Planning, Process, and Production by Alexander Brandon";
                    actionableAdvice = "Study how audio affects player psychology and behavior. Build a diverse sound library and learn multiple audio tools. Always test your audio in actual gameplay contexts, not just in isolation.";
                }
            } else if (primaryCategory === 'narrative_score') {
                if (scores.creative_score >= 8) {
                    role = "Narrative Designer";
                    description = "You're the storyteller who creates interactive narratives that respond to player choices. Like the designers behind Disco Elysium, The Stanley Parable, or Mass Effect, you understand that game stories aren't just told - they're experienced, shaped by player agency and meaningful choices.";
                    strengths = ["Interactive storytelling expertise", "Character development skills", "Branching narrative design", "Player psychology understanding"];
                    weaknesses = ["May create overly complex narrative systems", "Risk of prioritizing story over gameplay", "Tendency to over-write dialogue"];
                    bookRecommendation = "Character Development and Storytelling for Games by Lee Sheldon";
                    actionableAdvice = "Learn to write for interactivity, not just linearity. Study how successful games integrate story and gameplay. Always consider the player's agency in your narrative designs.";
                } else if (scores.systems_score >= 6) {
                    role = "Quest Designer";
                    description = "You're the architect of player journeys - designing quests, missions, and content that feel meaningful rather than like chores. Whether creating the intricate quest webs of The Witcher 3, the emergent stories of Skyrim, or the narrative missions of Red Dead Redemption, you structure adventure.";
                    strengths = ["Quest structure expertise", "Progression system design", "Player motivation understanding", "Content organization skills"];
                    weaknesses = ["May create repetitive quest patterns", "Risk of over-systematizing narrative content", "Tendency to focus on mechanics over story"];
                    bookRecommendation = "The Hero with a Thousand Faces by Joseph Campbell";
                    actionableAdvice = "Study player motivation and reward psychology. Create quest templates that feel varied despite structural similarities. Always consider the emotional arc of your quest chains.";
                } else {
                    role = "Writer";
                    description = "You're the wordsmith who brings game worlds to life through dialogue, lore, and narrative text. Whether crafting the witty banter of Portal, the deep lore of Dark Souls, or the character development of The Last of Us, your words create the voice and personality of entire universes.";
                    strengths = ["Strong writing fundamentals", "Character voice development", "World-building ability", "Dialogue crafting skills"];
                    weaknesses = ["May write too much text for game context", "Risk of creating inconsistent character voices", "Tendency to over-explain world details"];
                    bookRecommendation = "The Writer's Journey by Christopher Vogler";
                    actionableAdvice = "Learn to write concisely for interactive media. Develop distinct character voices and maintain consistency. Study how successful games integrate text with gameplay flow.";
                }
            } else if (primaryCategory === 'business_score') {
                if (scores.leadership_preference >= 8) {
                    role = "Executive Producer";
                    description = "You're the strategic leader who balances creative vision with business reality. Like the executive producers behind franchises like Assassin's Creed, Call of Duty, or The Witcher, you ensure great games actually reach players while maintaining quality and team morale.";
                    strengths = ["Strategic thinking", "Cross-functional leadership", "Business acumen", "Risk management"];
                    weaknesses = ["May prioritize business over creative vision", "Risk of becoming disconnected from development", "Tendency to over-manage creative processes"];
                    bookRecommendation = "Blood, Sweat, and Pixels by Jason Schreier";
                    actionableAdvice = "Stay connected to the development process and player feedback. Learn to communicate business constraints as creative challenges. Build strong relationships across all departments.";
                } else if (scores.social_score >= 6) {
                    role = "Product Manager";
                    description = "You're the bridge between what players want and what developers can build. Like the product managers behind successful live-service games or mobile hits, you translate market insights into development priorities while keeping teams aligned on player value.";
                    strengths = ["Market analysis skills", "Cross-team coordination", "User research expertise", "Priority management"];
                    weaknesses = ["May over-analyze instead of deciding", "Risk of feature creep", "Tendency to please everyone"];
                    bookRecommendation = "Inspired by Marty Cagan";
                    actionableAdvice = "Develop strong data analysis skills and learn to make decisions with incomplete information. Build relationships with players through community engagement. Practice saying no to good ideas that don't serve the core vision.";
                } else {
                    role = "Producer";
                    description = "You're the project coordinator who keeps game development on track. Like the producers behind successful AAA releases, you manage timelines, resources, and team dynamics to ensure games ship on time and on budget while maintaining quality and team sanity.";
                    strengths = ["Project management expertise", "Team coordination skills", "Problem-solving ability", "Communication skills"];
                    weaknesses = ["May become overwhelmed by competing priorities", "Risk of micromanaging teams", "Tendency to take on too much responsibility"];
                    bookRecommendation = "Postmortems from Game Developer Magazine";
                    actionableAdvice = "Learn multiple project management methodologies and adapt them to your team's needs. Develop strong emotional intelligence for managing team dynamics. Focus on removing obstacles rather than directing work.";
                }
            } else if (primaryCategory === 'social_score') {
                if (scores.business_score >= 6) {
                    role = "Community Manager";
                    description = "You're the vital link between developers and players, building communities around games. Like the community managers behind games like Warframe, Fall Guys, or Among Us, you turn players into passionate advocates and help shape games based on community feedback.";
                    strengths = ["Community building skills", "Social media expertise", "Player psychology understanding", "Crisis communication ability"];
                    weaknesses = ["May take negative feedback too personally", "Risk of over-promising to community", "Tendency to work outside normal hours"];
                    bookRecommendation = "The Cluetrain Manifesto by Rick Levine";
                    actionableAdvice = "Develop thick skin while maintaining empathy. Learn to set boundaries between work and personal time. Build strong internal relationships to advocate effectively for your community.";
                } else if (scores.leadership_preference >= 6) {
                    role = "Team Lead";
                    description = "You're the people-focused leader who brings out the best in development teams. Like the leads behind successful studios, you understand that great games come from great teams, and your job is creating environments where creativity and productivity flourish together.";
                    strengths = ["Team building skills", "Conflict resolution ability", "Mentoring expertise", "Collaborative leadership"];
                    weaknesses = ["May avoid difficult conversations", "Risk of being too accommodating", "Tendency to take on team members' stress"];
                    bookRecommendation = "The Culture Code by Daniel Coyle";
                    actionableAdvice = "Learn to have difficult conversations early before problems escalate. Develop your coaching skills to help team members grow. Set clear expectations while maintaining psychological safety.";
                } else {
                    role = "QA Lead";
                    description = "You're the quality guardian who ensures players have great first experiences with games. Like the QA leads behind polished releases, you coordinate testing efforts and build processes that catch problems before they reach players.";
                    strengths = ["Attention to detail", "Systematic thinking", "Player empathy", "Communication skills"];
                    weaknesses = ["May become overwhelmed by bug volume", "Risk of perfectionism blocking releases", "Tendency to focus on problems over solutions"];
                    bookRecommendation = "Lessons Learned in Software Testing by Cem Kaner";
                    actionableAdvice = "Learn to prioritize bugs by player impact, not just severity. Develop automation skills to scale your testing efforts. Build strong relationships with developers to improve bug prevention.";
                }
            } else if (primaryCategory === 'systems_score') {
                if (scores.technical_score >= 8) {
                    role = "Systems Programmer";
                    description = "You're the programmer who builds the complex, interconnected systems that make games work. Like the engineers behind the AI systems in F.E.A.R., the physics in Half-Life 2, or the procedural generation in No Man's Sky, you create the invisible logic that enables gameplay.";
                    strengths = ["Complex system design", "Performance optimization", "Debugging expertise", "Mathematical problem-solving"];
                    weaknesses = ["May over-engineer simple solutions", "Risk of creating systems too complex for others", "Tendency to work in isolation"];
                    bookRecommendation = "Structure and Interpretation of Computer Programs by Harold Abelson";
                    actionableAdvice = "Document your systems thoroughly and create clear APIs for other developers. Balance elegance with maintainability. Regularly step back to ensure your systems serve the game's needs, not just technical perfection.";
                } else {
                    role = "Systems Designer";
                    description = "You're the designer who creates interconnected game systems that generate emergent, engaging gameplay. Like the designers behind Civilization's tech trees, Minecraft's crafting systems, or Dwarf Fortress's simulation depth, you craft rules that create endless possibilities.";
                    strengths = ["Systems thinking", "Balance intuition", "Player psychology understanding", "Iterative design skills"];
                    weaknesses = ["May create overly complex systems", "Risk of designing for edge cases", "Tendency to over-balance systems"];
                    bookRecommendation = "A New Kind of Science by Stephen Wolfram";
                    actionableAdvice = "Start with simple systems and add complexity only when needed. Playtest extensively to understand how players actually interact with your systems. Learn basic programming to better communicate with developers.";
                }
            }

            // Generate personality insights
            let personalityInsights = [];

            if (scores.collaboration_style >= 8) {
                personalityInsights.push("You work best in collaborative environments where you can share ideas and build on others' creativity.");
            }

            if (scores.leadership_preference >= 8) {
                personalityInsights.push(`Your leadership tendencies suggest you might also consider roles like ${role} Lead or department management positions.`);
            }

            if (scores.innovation_drive >= 10) {
                personalityInsights.push("Your drive for innovation means you'd be particularly drawn to emerging technologies like VR/AR, AI-assisted development, or experimental gameplay mechanics.");
            }

            if (scores.detail_orientation >= 8) {
                personalityInsights.push("Your attention to detail makes you well-suited for roles requiring precision and thorough analysis.");
            }

            if (scores.user_focus >= 8) {
                personalityInsights.push("Your strong user focus means you naturally consider the player experience in all your decisions.");
            }

            const quizContent = document.getElementById('quizContent');

            // Get book recommendation for this role
            const recommendedBook = roleBookMapping[role];
            const bookSection = recommendedBook ? generateBookSection(recommendedBook) : '';

            quizContent.innerHTML = `
                <div class="results-container">
                    <h2 class="results-title">🎮 You Think Like A...</h2>
                    <div class="results-role">${role}</div>
                    <p class="results-description">${description.replace("You're the", "You think like the").replace("You see", "You would see").replace("You understand", "You naturally understand")}</p>

                    ${salaryRange ? `
                        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4caf50;">
                            <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">💰 Salary Expectations</h3>
                            <p style="margin: 0; color: #2e7d32; font-weight: 600; line-height: 1.5;">${salaryRange}</p>
                            ${careerProgression ? `<p style="margin: 10px 0 0 0; color: #2e7d32; font-size: 0.95rem;">${careerProgression}</p>` : ''}
                        </div>
                    ` : ''}

                    ${first30Days.length > 0 ? `
                        <div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff9800;">
                            <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">🚀 Your First 30 Days</h3>
                            <ul style="margin: 0; padding-left: 20px; color: #e65100;">
                                ${first30Days.map(day => `<li style="margin: 8px 0; line-height: 1.5;">${day}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${portfolioEssentials.length > 0 ? `
                        <div style="background: #f3e5f5; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #9c27b0;">
                            <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">💼 Portfolio Must-Haves</h3>
                            <ul style="margin: 0; padding-left: 20px; color: #4a148c;">
                                ${portfolioEssentials.map(item => `<li style="margin: 8px 0; line-height: 1.5;">${item}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${fundamentalSkills.length > 0 ? `
                        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;">
                            <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">🔧 Fundamental Skills to Master</h3>
                            <ul style="margin: 0; padding-left: 20px; color: #0d47a1;">
                                ${fundamentalSkills.map(skill => `<li style="margin: 8px 0; line-height: 1.5;">${skill}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${strengths.length > 0 ? `
                        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4caf50;">
                            <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">✨ Your Natural Strengths</h3>
                            <ul style="margin: 0; padding-left: 20px; color: #2e7d32;">
                                ${strengths.map(strength => `<li style="margin: 8px 0; line-height: 1.5;">${strength}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${weaknesses.length > 0 ? `
                        <div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ff9800;">
                            <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">⚠️ Areas for Growth</h3>
                            <ul style="margin: 0; padding-left: 20px; color: #e65100;">
                                ${weaknesses.map(weakness => `<li style="margin: 8px 0; line-height: 1.5;">${weakness}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${bookSection}

                    ${actionableAdvice ? `
                        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3;">
                            <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">🎯 Your Action Plan</h3>
                            <p style="margin: 0; color: #0d47a1; line-height: 1.6;">${actionableAdvice}</p>
                        </div>
                    ` : ''}

                    ${personalityInsights.length > 0 ? `
                        <div style="background: #f8f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
                            <h3 style="margin: 0 0 15px 0; color: #1a1a1a; font-size: 1.2rem;">💡 Your Work Style</h3>
                            ${personalityInsights.map(insight => `<p style="margin: 10px 0; color: #666; line-height: 1.5;">${insight}</p>`).join('')}
                        </div>
                    ` : ''}

                    <div class="score-breakdown">
                        <h3 style="grid-column: 1 / -1; text-align: center; margin-bottom: 20px; color: #1a1a1a;">Your Skill Profile</h3>
                        ${topCategories.slice(0, 4).map(([category, score]) => `
                            <div class="score-item">
                                <div class="score-label">${category.replace('_score', '').replace('_', ' ').toUpperCase()}</div>
                                <div class="score-value">${score}</div>
                            </div>
                        `).join('')}
                    </div>

                    <div style="margin-top: 30px; padding: 20px; background: #f0f0f0; border-radius: 8px; text-align: center;">
                        <h3 style="margin: 0 0 15px 0; color: #1a1a1a;">🚀 Next Steps</h3>
                        <p style="margin: 0; color: #666; line-height: 1.5;">
                            Ready to level up as a ${role}? Start by reading your recommended book, then build a portfolio showcasing your ${topCategories[0][0].replace('_score', '').replace('_', ' ')} skills.
                            Connect with professionals in this field - the game dev community is surprisingly welcoming to newcomers who show genuine passion.
                        </p>
                    </div>

                    <button class="btn btn-primary" onclick="location.reload()" style="margin-top: 30px;">Take Quiz Again</button>
                </div>
            `;

            // Add footer disclosure if not already present
            const container = document.querySelector('.quiz-container');
            if (container && !document.querySelector('.quiz-footer-disclosure')) {
                const footer = document.createElement('div');
                footer.className = 'quiz-footer-disclosure';
                footer.innerHTML = '💡 <strong>Supporting This Resource:</strong> This free quiz is supported by affiliate commissions from book recommendations. When you purchase recommended books through our links, you help fund the creation of more career guidance resources. All book recommendations are chosen for their genuine value to game developers.';
                container.appendChild(footer);
            }

            document.querySelector('.navigation').style.display = 'none';
        }

        // Initialize
        console.log('Quiz initialized');
        updateNavigation();
    </script>
</body>
</html>
