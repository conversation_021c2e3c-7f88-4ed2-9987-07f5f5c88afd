// Business and Narrative Role Definitions for Game Dev Career Quiz
// Focus: Fundamentals-first approach, practical business and storytelling guidance

const businessNarrativeRoles = {
    // NARRATIVE ROLES
    "Narrative Designer": {
        description: "You're the storyteller who creates interactive narratives that respond to player choices. Like the designers behind Disco Elysium, The Stanley Parable, or Mass Effect, you understand that game stories aren't just told - they're experienced, shaped by player agency and meaningful choices.",
        
        strengths: [
            "Interactive storytelling expertise",
            "Character development skills",
            "Branching narrative design",
            "Player psychology understanding"
        ],
        
        weaknesses: [
            "May create overly complex narrative systems",
            "Risk of prioritizing story over gameplay",
            "Tendency to over-write dialogue"
        ],
        
        first30Days: [
            "Week 1: Write a branching dialogue tree with 3 meaningful player choices",
            "Week 2: Create character profiles with distinct voices and motivations",
            "Week 3: Design a quest that tells story through player actions, not exposition",
            "Week 4: Study how 3 games integrate narrative with gameplay mechanics"
        ],
        
        portfolioEssentials: [
            "Interactive dialogue system with meaningful player choices",
            "Character writing samples showing distinct voices",
            "Branching narrative flowchart with consequence tracking",
            "Environmental storytelling examples (story told through world design)",
            "Analysis of how narrative supports gameplay in existing games"
        ],
        
        fundamentalSkills: [
            "Master dialogue writing and character voice development",
            "Learn branching narrative tools and flowchart design",
            "Understand player agency and meaningful choice design",
            "Study interactive storytelling techniques across media",
            "Practice writing concisely for interactive contexts"
        ],
        
        bookRecommendation: "Character Development and Storytelling for Games by Lee Sheldon",
        actionableAdvice: "Learn to write for interactivity, not just linearity. Study how successful games integrate story and gameplay. Always consider the player's agency in your narrative designs."
    },

    "Quest Designer": {
        description: "You're the architect of player journeys - designing quests, missions, and content that feel meaningful rather than like chores. Whether creating the intricate quest webs of The Witcher 3, the emergent stories of Skyrim, or the narrative missions of Red Dead Redemption, you structure adventure.",
        
        strengths: [
            "Quest structure expertise",
            "Progression system design",
            "Player motivation understanding",
            "Content organization skills"
        ],
        
        weaknesses: [
            "May create repetitive quest patterns",
            "Risk of over-systematizing narrative content",
            "Tendency to focus on mechanics over story"
        ],
        
        first30Days: [
            "Week 1: Design 5 different quest types that avoid 'fetch quest' patterns",
            "Week 2: Create a quest chain that builds emotional investment over time",
            "Week 3: Map out player progression and reward pacing for a game area",
            "Week 4: Analyze quest design in 3 different RPGs - what makes them engaging?"
        ],
        
        portfolioEssentials: [
            "Quest design document with clear objectives and player motivation",
            "Quest chain showing escalating stakes and emotional investment",
            "Player progression system with meaningful rewards and choices",
            "Content organization system for large-scale quest management",
            "Analysis of successful quest design with improvement suggestions"
        ],
        
        fundamentalSkills: [
            "Master quest structure and pacing techniques",
            "Learn player motivation and reward psychology",
            "Understand content organization and management systems",
            "Study successful RPG and adventure game design",
            "Practice balancing narrative goals with gameplay constraints"
        ],
        
        bookRecommendation: "The Hero with a Thousand Faces by Joseph Campbell",
        actionableAdvice: "Study player motivation and reward psychology. Create quest templates that feel varied despite structural similarities. Always consider the emotional arc of your quest chains."
    },

    "Writer": {
        description: "You're the wordsmith who brings game worlds to life through dialogue, lore, and narrative text. Whether crafting the witty banter of Portal, the deep lore of Dark Souls, or the character development of The Last of Us, your words create the voice and personality of entire universes.",
        
        strengths: [
            "Strong writing fundamentals",
            "Character voice development",
            "World-building ability",
            "Dialogue crafting skills"
        ],
        
        weaknesses: [
            "May write too much text for game context",
            "Risk of creating inconsistent character voices",
            "Tendency to over-explain world details"
        ],
        
        first30Days: [
            "Week 1: Write dialogue for 3 characters with completely different speaking styles",
            "Week 2: Create a world bible with consistent lore and terminology",
            "Week 3: Practice writing game text with strict character limits",
            "Week 4: Study how successful games use text to enhance rather than interrupt gameplay"
        ],
        
        portfolioEssentials: [
            "Dialogue samples showing distinct character voices",
            "World-building document with consistent internal logic",
            "Game text examples (UI, item descriptions, environmental text)",
            "Adaptation exercise (book/film scene rewritten for interactive medium)",
            "Style guide showing consistent tone and voice across different content types"
        ],
        
        fundamentalSkills: [
            "Master dialogue writing and character voice consistency",
            "Learn to write concisely for interactive media constraints",
            "Understand world-building and internal consistency",
            "Study how text integrates with gameplay flow",
            "Practice writing for different game genres and audiences"
        ],
        
        bookRecommendation: "The Writer's Journey by Christopher Vogler",
        actionableAdvice: "Learn to write concisely for interactive media. Develop distinct character voices and maintain consistency. Study how successful games integrate text with gameplay flow."
    },

    // BUSINESS ROLES
    "Executive Producer": {
        description: "You're the strategic leader who balances creative vision with business reality. Like the executive producers behind franchises like Assassin's Creed, Call of Duty, or The Witcher, you ensure great games actually reach players while maintaining quality and team morale.",
        
        strengths: [
            "Strategic thinking",
            "Cross-functional leadership",
            "Business acumen",
            "Risk management"
        ],
        
        weaknesses: [
            "May prioritize business over creative vision",
            "Risk of becoming disconnected from development",
            "Tendency to over-manage creative processes"
        ],
        
        first30Days: [
            "Week 1: Create a project timeline with realistic milestones and dependencies",
            "Week 2: Practice presenting game concepts to different stakeholder audiences",
            "Week 3: Study successful game launches - what made them work?",
            "Week 4: Learn basic game development processes across all disciplines"
        ],
        
        portfolioEssentials: [
            "Project management examples with successful outcomes",
            "Business case analysis for game concepts",
            "Risk assessment and mitigation strategies",
            "Cross-functional team coordination examples",
            "Market analysis and competitive positioning documents"
        ],
        
        fundamentalSkills: [
            "Master project management methodologies and tools",
            "Learn game industry business models and economics",
            "Understand all aspects of game development pipeline",
            "Study market analysis and competitive intelligence",
            "Practice strategic communication and stakeholder management"
        ],
        
        bookRecommendation: "Blood, Sweat, and Pixels by Jason Schreier",
        actionableAdvice: "Stay connected to the development process and player feedback. Learn to communicate business constraints as creative challenges. Build strong relationships across all departments."
    },

    "Product Manager": {
        description: "You're the bridge between what players want and what developers can build. Like the product managers behind successful live-service games or mobile hits, you translate market insights into development priorities while keeping teams aligned on player value.",
        
        strengths: [
            "Market analysis skills",
            "Cross-team coordination",
            "User research expertise",
            "Priority management"
        ],
        
        weaknesses: [
            "May over-analyze instead of deciding",
            "Risk of feature creep",
            "Tendency to please everyone"
        ],
        
        first30Days: [
            "Week 1: Conduct user research on a game you play regularly",
            "Week 2: Create a feature prioritization framework with clear criteria",
            "Week 3: Practice translating user feedback into actionable development tasks",
            "Week 4: Study successful product launches in gaming - what drove their decisions?"
        ],
        
        portfolioEssentials: [
            "User research study with actionable insights",
            "Feature prioritization framework with clear decision criteria",
            "Product roadmap balancing user needs and business goals",
            "A/B testing plan and results analysis",
            "Cross-functional project coordination examples"
        ],
        
        fundamentalSkills: [
            "Master user research and data analysis techniques",
            "Learn product management frameworks and prioritization methods",
            "Understand game analytics and player behavior metrics",
            "Study successful product strategies in gaming",
            "Practice clear communication across technical and creative teams"
        ],
        
        bookRecommendation: "Inspired by Marty Cagan",
        actionableAdvice: "Develop strong data analysis skills and learn to make decisions with incomplete information. Build relationships with players through community engagement. Practice saying no to good ideas that don't serve the core vision."
    },

    "Producer": {
        description: "You're the project coordinator who keeps game development on track. Like the producers behind successful AAA releases, you manage timelines, resources, and team dynamics to ensure games ship on time and on budget while maintaining quality and team sanity.",
        
        strengths: [
            "Project management expertise",
            "Team coordination skills",
            "Problem-solving ability",
            "Communication skills"
        ],
        
        weaknesses: [
            "May become overwhelmed by competing priorities",
            "Risk of micromanaging teams",
            "Tendency to take on too much responsibility"
        ],
        
        first30Days: [
            "Week 1: Learn a project management tool and create a sample game development timeline",
            "Week 2: Practice running team meetings with clear agendas and outcomes",
            "Week 3: Study game development methodologies (Agile, Scrum, etc.)",
            "Week 4: Interview developers about their biggest project management pain points"
        ],
        
        portfolioEssentials: [
            "Project timeline with realistic milestones and risk assessment",
            "Team coordination examples with measurable outcomes",
            "Problem-solving case studies from actual projects",
            "Process improvement documentation with before/after metrics",
            "Communication examples across different stakeholder groups"
        ],
        
        fundamentalSkills: [
            "Master multiple project management methodologies",
            "Learn team dynamics and conflict resolution",
            "Understand all aspects of game development pipeline",
            "Study risk management and contingency planning",
            "Practice clear communication and expectation setting"
        ],
        
        bookRecommendation: "Postmortems from Game Developer Magazine",
        actionableAdvice: "Learn multiple project management methodologies and adapt them to your team's needs. Develop strong emotional intelligence for managing team dynamics. Focus on removing obstacles rather than directing work."
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = businessNarrativeRoles;
}
