# Typeform Implementation Guide - Game Dev Career Quiz

## Step 1: Set Up Variables (Admin Panel → Logic → Variables)

### Core Category Variables
```
creative_score = 0
technical_score = 0  
visual_score = 0
audio_score = 0
social_score = 0
business_score = 0
narrative_score = 0
systems_score = 0
```

### Behavioral Trait Variables
```
leadership_preference = 0
collaboration_style = 0
detail_orientation = 0
innovation_drive = 0
user_focus = 0
```

### Result Calculation Variables (we'll use these later)
```
top_category = ""
secondary_category = ""
primary_role = ""
personalized_message = ""
```

---

## Step 2: Question Setup & Logic Rules

### Question 1: Multiple Choice (Allow 1-2 selections)
**Question:** "What energizes you most when starting a new project?"

**In Logic → Question Logic:**

**IF** "Sketching out the big picture and overall vision" **IS SELECTED**
- **THEN** Calculate: `creative_score = creative_score + 3`
- **AND** Calculate: `business_score = business_score + 1` 
- **AND** Calculate: `leadership_preference = leadership_preference + 2`

**IF** "Diving deep into technical architecture" **IS SELECTED**
- **THEN** Calculate: `technical_score = technical_score + 3`
- **AND** Calculate: `systems_score = systems_score + 2`
- **AND** Calculate: `detail_orientation = detail_orientation + 2`

**IF** "Creating visual concepts and mood boards" **IS SELECTED**
- **THEN** Calculate: `visual_score = visual_score + 3`
- **AND** Calculate: `creative_score = creative_score + 1`
- **AND** Calculate: `innovation_drive = innovation_drive + 1`

**IF** "Understanding the audience and their needs" **IS SELECTED**
- **THEN** Calculate: `business_score = business_score + 2`
- **AND** Calculate: `social_score = social_score + 2`
- **AND** Calculate: `systems_score = systems_score + 1`
- **AND** Calculate: `user_focus = user_focus + 3`

**IF** "Planning the narrative and emotional journey" **IS SELECTED**
- **THEN** Calculate: `narrative_score = narrative_score + 3`
- **AND** Calculate: `creative_score = creative_score + 1`
- **AND** Calculate: `user_focus = user_focus + 2`

---

### Question 2: Ranking (Top 3 out of 6)
**Question:** "Rank these work activities by preference (top 3)"

**In Logic → Question Logic:**

**IF** "Solving complex technical problems" **IS RANKED 1ST**
- **THEN** Calculate: `technical_score = technical_score + 3`
- **AND** Calculate: `detail_orientation = detail_orientation + 2`

**IF** "Solving complex technical problems" **IS RANKED 2ND**
- **THEN** Calculate: `technical_score = technical_score + 2`
- **AND** Calculate: `systems_score = systems_score + 1`

**IF** "Solving complex technical problems" **IS RANKED 3RD**
- **THEN** Calculate: `technical_score = technical_score + 1`

**IF** "Creating visual designs and artwork" **IS RANKED 1ST**
- **THEN** Calculate: `visual_score = visual_score + 3`
- **AND** Calculate: `creative_score = creative_score + 2`

**IF** "Creating visual designs and artwork" **IS RANKED 2ND**
- **THEN** Calculate: `visual_score = visual_score + 2`
- **AND** Calculate: `creative_score = creative_score + 1`

**IF** "Creating visual designs and artwork" **IS RANKED 3RD**
- **THEN** Calculate: `visual_score = visual_score + 1`

*(Continue this pattern for all 6 ranking options)*

---

### Question 4: Opinion Scale (1-5 for each item)
**Question:** "Rate your interest level (1=Not interested, 5=Very interested)"

**In Logic → Question Logic:**

**For "Deep technical implementation":**
- **IF** Rating **= 5**: Calculate `technical_score = technical_score + 3` AND `detail_orientation = detail_orientation + 2`
- **IF** Rating **= 4**: Calculate `technical_score = technical_score + 2.4` AND `detail_orientation = detail_orientation + 1.6`
- **IF** Rating **= 3**: Calculate `technical_score = technical_score + 1.8` AND `detail_orientation = detail_orientation + 1.2`
- **IF** Rating **= 2**: Calculate `technical_score = technical_score + 1.2` AND `detail_orientation = detail_orientation + 0.8`
- **IF** Rating **= 1**: Calculate `technical_score = technical_score + 0.6` AND `detail_orientation = detail_orientation + 0.4`

*(Repeat for each scale item with their respective scoring)*

---

## Step 3: Conditional Question Flow

### Example: Follow-up Questions Based on High Scores

**After Question 6, add Logic:**

**IF** `technical_score > 15` **AND** `collaboration_style < 5`
- **THEN** Jump to: "Technical Solo Work Preference Question"

**IF** `creative_score > 12` **AND** `visual_score > 10`
- **THEN** Jump to: "Visual Arts Specialization Question"

**IF** `social_score > 15`
- **THEN** Jump to: "Team Dynamics Preference Question"

**Otherwise:** Continue to next standard question

---

## Step 4: Dynamic Result Calculation (Before Results Page)

### Primary Category Determination

**Create a "Hidden" calculation question before results:**

**In Logic → Question Logic:**

**IF** `technical_score >= creative_score` **AND** `technical_score >= visual_score` **AND** `technical_score >= audio_score` **AND** `technical_score >= social_score` **AND** `technical_score >= business_score` **AND** `technical_score >= narrative_score` **AND** `technical_score >= systems_score`
- **THEN** Calculate: `top_category = "Technical"`

**IF** `creative_score >= technical_score` **AND** `creative_score >= visual_score` **AND** `creative_score >= audio_score` **AND** `creative_score >= social_score` **AND** `creative_score >= business_score` **AND** `creative_score >= narrative_score` **AND** `creative_score >= systems_score`
- **THEN** Calculate: `top_category = "Creative"`

*(Continue for all 8 categories)*

---

### Role-Specific Calculations

**After determining top_category, add more specific role logic:**

**IF** `top_category = "Technical"` **AND** `systems_score >= 15` **AND** `leadership_preference >= 10`
- **THEN** Calculate: `primary_role = "Technical Director"`

**IF** `top_category = "Technical"` **AND** `systems_score >= 15` **AND** `innovation_drive >= 10`
- **THEN** Calculate: `primary_role = "Tools Programmer"`

**IF** `top_category = "Technical"` **AND** `systems_score >= 15`
- **THEN** Calculate: `primary_role = "Gameplay Programmer"`

**IF** `top_category = "Creative"` **AND** `visual_score >= 15`
- **THEN** Calculate: `primary_role = "Art Director"`

**IF** `top_category = "Creative"` **AND** `narrative_score >= 15`
- **THEN** Calculate: `primary_role = "Creative Director"`

*(Continue for all role combinations)*

---

## Step 5: Personalized Result Messages

### Dynamic Text Variables in Results

**In your Results page, use recall variables:**

```
Congratulations! Based on your responses, you're strongly aligned with: 

**{{primary_role}}**

Your combination of high {{top_category}} interests (score: {{technical_score}}) and strong {{secondary_category}} skills suggests you'd thrive in roles that bridge technical complexity with creative problem-solving.
```

### Conditional Result Text Blocks

**Add Logic to Results page:**

**IF** `collaboration_style >= 8`
- **THEN** Show text: "You work best in collaborative environments where you can share ideas and build on others' creativity."

**IF** `leadership_preference >= 8`
- **THEN** Show text: "Your leadership tendencies suggest you might also consider roles like {{primary_role}} Lead or Studio Director."

**IF** `innovation_drive >= 10`
- **THEN** Show text: "Your drive for innovation means you'd be particularly drawn to emerging technologies like VR/AR, AI-assisted development, or experimental gameplay mechanics."

---

## Step 6: Testing & Validation

### Test Scenarios to Verify:

1. **Technical Focus Path:**
   - Select mostly technical answers
   - Verify technical_score increases correctly
   - Check that result shows appropriate technical role

2. **Creative-Visual Hybrid:**
   - Select creative + visual answers  
   - Verify both scores increase
   - Check for Art Director or Creative Director result

3. **Edge Cases:**
   - Select equal amounts across categories
   - Verify tie-breaking logic works
   - Check that secondary category influences results

### Debug Variables Display (Temporary)
Add a results page that shows all variable values for testing:
```
Technical: {{technical_score}}
Creative: {{creative_score}}
Visual: {{visual_score}}
Leadership: {{leadership_preference}}
Top Category: {{top_category}}
Primary Role: {{primary_role}}
```

---

## Step 7: Advanced Features

### Progress-Based Encouragement

**After Question 3:**
**IF** `creative_score > 6`
- **THEN** Show: "Your creative instincts are already showing! 🎨"

**After Question 6:**
**IF** `technical_score > 12`
- **THEN** Show: "You're demonstrating strong technical thinking! ⚙️"

### Smart Question Ordering

**IF** previous answers show strong preferences, prioritize related questions:

**IF** `visual_score > audio_score + 5` by Question 4
- **THEN** Show visual-focused questions next
- **ELSE** Show balanced mix

---

## Implementation Checklist

- [ ] Set up all 13 variables in Typeform
- [ ] Create all questions with proper answer options
- [ ] Add scoring logic to each question
- [ ] Set up conditional question flow
- [ ] Create role calculation logic
- [ ] Design results page with dynamic text
- [ ] Test all scoring paths
- [ ] Add progress encouragement messages
- [ ] Verify mobile responsiveness
- [ ] Set up analytics tracking

**Pro Tip:** Start with 3-4 questions to test the logic system, then gradually add more questions once you've verified the scoring works correctly!