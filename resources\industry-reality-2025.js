// Game Development Industry Reality Check - 2025 Edition
// Focus: Honest career guidance, salary expectations, and breaking-in strategies

const industryReality2025 = {
    salaryRanges: {
        // All figures in USD, based on 2024-2025 industry data
        "Technical Director": {
            entry: "Not typically entry-level - requires 8+ years experience",
            mid: "$140,000 - $180,000",
            senior: "$180,000 - $250,000+",
            notes: "Usually promoted from Senior Programmer roles. Requires proven leadership."
        },
        
        "Tools Programmer": {
            entry: "$75,000 - $95,000",
            mid: "$95,000 - $130,000", 
            senior: "$130,000 - $170,000",
            notes: "High demand role. Often hired at mid-level with general programming experience."
        },
        
        "Engine Programmer": {
            entry: "$80,000 - $100,000",
            mid: "$100,000 - $140,000",
            senior: "$140,000 - $180,000",
            notes: "Requires strong C++ and math skills. Competitive but well-compensated."
        },
        
        "Gameplay Programmer": {
            entry: "$70,000 - $90,000",
            mid: "$90,000 - $120,000",
            senior: "$120,000 - $160,000",
            notes: "Most common entry point for programming roles. Good work-life balance."
        },
        
        "Game Designer": {
            entry: "$55,000 - $75,000",
            mid: "$75,000 - $100,000",
            senior: "$100,000 - $140,000",
            notes: "Highly competitive entry-level. Portfolio and shipped games crucial."
        },
        
        "Visual Artist": {
            entry: "$50,000 - $70,000",
            mid: "$70,000 - $95,000",
            senior: "$95,000 - $130,000",
            notes: "Wide range based on specialization. Concept artists typically earn more."
        },
        
        "Technical Artist": {
            entry: "$65,000 - $85,000",
            mid: "$85,000 - $115,000",
            senior: "$115,000 - $150,000",
            notes: "High demand, limited supply. Excellent career growth potential."
        },
        
        "Audio Designer": {
            entry: "$50,000 - $70,000",
            mid: "$70,000 - $95,000",
            senior: "$95,000 - $125,000",
            notes: "Smaller field, often contract work. Audio programmers earn more."
        },
        
        "Producer": {
            entry: "$60,000 - $80,000",
            mid: "$80,000 - $110,000",
            senior: "$110,000 - $150,000",
            notes: "Path to executive roles. Requires strong communication and organization."
        }
    },

    careerProgression: {
        typical: [
            "Junior (0-2 years) - Learning fundamentals, following directions",
            "Mid-level (2-5 years) - Independent work, mentoring juniors",
            "Senior (5-8 years) - Technical leadership, architecture decisions",
            "Lead (8+ years) - Team management, cross-discipline coordination",
            "Director (10+ years) - Department leadership, strategic planning"
        ],
        
        reality: [
            "Most people don't follow linear progression paths",
            "Switching companies often accelerates salary growth",
            "Specialization vs. generalization depends on company size",
            "Leadership roles require different skills than individual contributor roles",
            "Industry experience matters more than years of general programming"
        ]
    },

    entryLevelTruth: {
        competition: [
            "Entry-level positions receive 100-500+ applications",
            "Portfolio quality matters more than formal education",
            "Networking and referrals significantly improve odds",
            "Many successful developers are self-taught",
            "Internships and game jams provide valuable connections"
        ],
        
        firstJobReality: [
            "Your first job probably won't be at your dream studio",
            "Smaller studios often provide better learning opportunities",
            "Contract and temporary positions can lead to full-time offers",
            "Geographic flexibility greatly increases opportunities",
            "Remote work is increasingly common but still competitive"
        ],
        
        portfolioRequirements: [
            "2-3 high-quality projects better than 10 mediocre ones",
            "Show progression and learning in your work",
            "Include source code and detailed explanations",
            "Demonstrate ability to finish projects",
            "Tailor portfolio to specific job applications"
        ]
    },

    industryRealities: {
        workLifeBalance: [
            "Crunch still exists but is less accepted than in the past",
            "Indie studios often have better work-life balance",
            "Remote work has improved quality of life for many",
            "Project-based work means periods of intensity",
            "Burnout is common - prioritize sustainable practices"
        ],
        
        jobSecurity: [
            "Game industry has higher turnover than other tech sectors",
            "Layoffs are common, especially after project completion",
            "Diversifying skills increases job security",
            "Building a professional network is crucial",
            "Having savings for job transitions is important"
        ],
        
        geographicConsiderations: [
            "Major hubs: Los Angeles, San Francisco, Seattle, Austin, Montreal",
            "Remote work has reduced geographic constraints",
            "Cost of living varies dramatically by location",
            "Some countries offer tax incentives for game development",
            "Visa requirements can limit international opportunities"
        ]
    },

    breakingInStrategies: {
        programming: [
            "Contribute to open-source game projects",
            "Participate in game jams regularly",
            "Build tools that solve real problems for developers",
            "Create technical blog posts about your learning",
            "Apply to QA positions as stepping stones"
        ],
        
        design: [
            "Mod existing games to show design skills",
            "Create paper prototypes and playtest extensively",
            "Write detailed design documents for original concepts",
            "Analyze existing games and suggest improvements",
            "Start as QA or community management to get inside"
        ],
        
        art: [
            "Study and recreate art from games you admire",
            "Learn the technical pipeline for your chosen specialty",
            "Create fan art that shows understanding of game constraints",
            "Build a diverse portfolio showing range and consistency",
            "Consider adjacent industries (film, advertising) as stepping stones"
        ],
        
        audio: [
            "Create sound packs and music for indie developers",
            "Implement your audio in actual game projects",
            "Study how successful games use audio to enhance gameplay",
            "Learn audio programming to increase versatility",
            "Offer to do audio for student and indie projects"
        ]
    },

    redFlags: {
        jobPostings: [
            "'Rockstar developer' - often means overwork expected",
            "'Competitive salary' - usually means below market rate",
            "'Fast-paced environment' - may indicate poor planning",
            "'Wear many hats' - could mean unclear role boundaries",
            "'Passion for gaming required' - sometimes used to justify low pay"
        ],
        
        interviews: [
            "Excessive unpaid 'test' work (more than 4-8 hours)",
            "Vague answers about work-life balance",
            "High turnover in the role you're applying for",
            "Unrealistic timeline expectations",
            "Lack of clear career progression paths"
        ],
        
        companies: [
            "No shipped games or long development cycles",
            "Frequent layoffs or restructuring",
            "Poor Glassdoor reviews mentioning management issues",
            "Unclear business model or funding",
            "Toxic community around their games"
        ]
    },

    alternativePaths: {
        adjacentIndustries: [
            "Simulation software (medical, military, training)",
            "Educational technology and serious games",
            "VR/AR applications beyond gaming",
            "Interactive media and digital art installations",
            "Gamification for business applications"
        ],
        
        freelanceConsulting: [
            "Technical consulting for indie developers",
            "Art and audio services for small studios",
            "Game design consulting and prototyping",
            "Educational content creation",
            "Tool development and licensing"
        ],
        
        entrepreneurship: [
            "Indie game development (high risk, high reward)",
            "Game development tools and services",
            "Educational content and courses",
            "Game-adjacent products (streaming tools, etc.)",
            "Consulting and contracting services"
        ]
    },

    successMetrics: {
        earlyCareer: [
            "Completing and shipping projects",
            "Building a professional network",
            "Developing a reputation for reliability",
            "Learning to work effectively in teams",
            "Understanding the full development pipeline"
        ],
        
        midCareer: [
            "Mentoring junior developers",
            "Leading significant features or systems",
            "Contributing to technical or creative decisions",
            "Building expertise in specialized areas",
            "Maintaining work-life balance"
        ],
        
        seniorCareer: [
            "Shaping product direction and vision",
            "Building and leading effective teams",
            "Contributing to industry knowledge and practices",
            "Balancing business needs with creative vision",
            "Developing the next generation of talent"
        ]
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = industryReality2025;
}
