# Game Development Career Quiz - Optimized Typeform Question Types

## Instructions for Users
**Choose 1-2 answers per question. Only select 2 if you strongly align with BOTH options. Fewer, more accurate selections will give you better results.**

---

## Question 1: What energizes you most when starting a new project?
**Type: Multiple Choice (Allow 1-2 selections)**
*Visual icons for each option enhance engagement*

🎨 **Sketching out the big picture and overall vision**
```
creative_score += 3, business_score += 1, leadership_preference += 2
```

⚙️ **Diving deep into technical architecture and problem-solving**
```
technical_score += 3, systems_score += 2, detail_orientation += 2
```

🎭 **Creating visual concepts and mood boards**
```
visual_score += 3, creative_score += 1, innovation_drive += 1
```

👥 **Understanding the audience and their needs**
```
business_score += 2, social_score += 2, systems_score += 1, user_focus += 3
```

📖 **Planning the narrative and emotional journey**
```
narrative_score += 3, creative_score += 1, user_focus += 2
```

---

## Question 2: Rank these work activities by preference
**Type: Ranking (Top 3 out of 6)**
*Ranking gives weighted scores: 1st choice = 3pts, 2nd = 2pts, 3rd = 1pt*

**Drag to rank your top 3 preferences:**

🔧 **Solving complex technical problems**
```
1st: technical_score += 3, detail_orientation += 2
2nd: technical_score += 2, systems_score += 1
3rd: technical_score += 1
```

🎨 **Creating visual designs and artwork**
```
1st: visual_score += 3, creative_score += 2
2nd: visual_score += 2, creative_score += 1
3rd: visual_score += 1
```

📝 **Writing stories and dialogue**
```
1st: narrative_score += 3, creative_score += 1
2nd: narrative_score += 2, user_focus += 1
3rd: narrative_score += 1
```

🎵 **Designing sounds and music**
```
1st: audio_score += 3, creative_score += 1
2nd: audio_score += 2, detail_orientation += 1
3rd: audio_score += 1
```

📊 **Analyzing data and user behavior**
```
1st: business_score += 3, systems_score += 2
2nd: business_score += 2, user_focus += 1
3rd: business_score += 1
```

🤝 **Leading teams and coordinating projects**
```
1st: social_score += 3, leadership_preference += 3
2nd: business_score += 2, collaboration_style += 2
3rd: social_score += 1, leadership_preference += 1
```

---

## Question 3: How do you prefer to receive feedback?
**Type: Multiple Choice (Allow 1-2 selections)**

💻 **Technical peer review with specific implementation suggestions**
```
technical_score += 3, detail_orientation += 2, collaboration_style += 1
```

🎨 **Creative direction and artistic vision guidance**
```
creative_score += 2, visual_score += 2, audio_score += 1, innovation_drive += 2
```

❤️ **User reactions and emotional responses to your work**
```
narrative_score += 2, social_score += 2, user_focus += 3
```

📈 **Data-driven insights and performance metrics**
```
business_score += 3, systems_score += 2, detail_orientation += 2
```

🚀 **Recognition for innovation and pushing boundaries**
```
creative_score += 2, technical_score += 1, innovation_drive += 3
```

---

## Question 4: Rate your interest level in these aspects
**Type: Opinion Scale (1-5 scale for each item)**
*Scores multiply the scale value: 5 = full points, 3 = 60% points, 1 = 20% points*

**Rate from 1 (Not interested) to 5 (Very interested):**

🔬 **Deep technical implementation and architecture**
```
Scale 1-5: technical_score += (rating * 0.6), detail_orientation += (rating * 0.4)
```

🎭 **Visual storytelling and aesthetic design**
```
Scale 1-5: visual_score += (rating * 0.6), creative_score += (rating * 0.4)
```

📚 **Narrative development and character creation**
```
Scale 1-5: narrative_score += (rating * 0.6), user_focus += (rating * 0.3)
```

🎶 **Audio design and musical composition**
```
Scale 1-5: audio_score += (rating * 0.6), creative_score += (rating * 0.2)
```

👥 **Community building and user engagement**
```
Scale 1-5: social_score += (rating * 0.6), collaboration_style += (rating * 0.3)
```

📊 **Business strategy and market analysis**
```
Scale 1-5: business_score += (rating * 0.6), leadership_preference += (rating * 0.2)
```

---

## Question 5: What's your ideal team size?
**Type: Dropdown**
*Team size preference affects collaboration and leadership scoring*

**Select your preferred team size:**

👤 **Solo work (1 person)**
```
detail_orientation += 3, innovation_drive += 2, collaboration_style -= 1
```

👥 **Small team (2-5 people)**
```
collaboration_style += 2, creative_score += 1, technical_score += 1
```

👨‍👩‍👧‍👦 **Medium team (6-15 people)**
```
social_score += 2, business_score += 1, collaboration_style += 2
```

🏢 **Large team (16+ people)**
```
business_score += 3, leadership_preference += 2, social_score += 1
```

🔄 **I adapt well to any team size**
```
collaboration_style += 3, social_score += 1
```

---

## Question 6: Drag and drop your workflow preferences
**Type: Picture Choice**
*Visual workflow representations for better user engagement*

**Choose 1-2 workflow styles that resonate with you:**

🏃‍♂️ **Sprint-based with quick iterations**
```
technical_score += 2, business_score += 2, collaboration_style += 1
```

🎨 **Creative exploration with flexible timelines**
```
creative_score += 3, visual_score += 1, audio_score += 1, innovation_drive += 2
```

📋 **Structured phases with clear documentation**
```
business_score += 2, technical_score += 1, detail_orientation += 3
```

🔄 **Continuous feedback and user testing cycles**
```
systems_score += 2, user_focus += 3, social_score += 1
```

🚀 **Rapid prototyping and experimentation**
```
innovation_drive += 3, technical_score += 2, creative_score += 1
```

---

## Question 7: When do you do your best work?
**Type: Multiple Choice (Single selection)**
*Time preference can indicate work style and focus needs*

🌅 **Early morning when it's quiet**
```
detail_orientation += 2, technical_score += 1, creative_score += 1
```

☀️ **During regular business hours with team energy**
```
collaboration_style += 3, social_score += 2, business_score += 1
```

🌙 **Late evening when inspiration strikes**
```
creative_score += 2, visual_score += 1, audio_score += 1, innovation_drive += 2
```

⏰ **I'm productive at any time with the right focus**
```
business_score += 1, collaboration_style += 1, detail_orientation += 1
```

🔄 **In intense bursts with breaks between**
```
innovation_drive += 2, creative_score += 2, technical_score += 1
```

---

## Question 8: What motivates you most in your career?
**Type: Ranking (Top 2 out of 5)**
*Career motivation is crucial for role fit*

**Rank your top 2 career motivators:**

🧩 **Solving complex challenges that others can't**
```
1st: technical_score += 3, innovation_drive += 2
2nd: technical_score += 2, detail_orientation += 1
```

🎨 **Bringing creative visions to life**
```
1st: creative_score += 3, visual_score += 1, audio_score += 1
2nd: creative_score += 2, innovation_drive += 1
```

❤️ **Creating experiences that emotionally impact people**
```
1st: narrative_score += 3, user_focus += 2
2nd: narrative_score += 2, social_score += 1
```

📈 **Building products that reach millions**
```
1st: business_score += 3, social_score += 2
2nd: business_score += 2, leadership_preference += 1
```

👑 **Leading teams and shaping company direction**
```
1st: leadership_preference += 3, business_score += 2
2nd: social_score += 2, collaboration_style += 1
```

---

## Question 9: How do you approach learning new skills?
**Type: Multiple Choice (Allow 1-2 selections)**

📚 **Deep study of documentation and technical resources**
```
technical_score += 3, detail_orientation += 2
```

🎨 **Hands-on experimentation and creative exploration**
```
creative_score += 2, visual_score += 1, audio_score += 1, innovation_drive += 2
```

👨‍🏫 **Learning from mentors and peer collaboration**
```
social_score += 3, collaboration_style += 3
```

📊 **Analyzing case studies and industry examples**
```
business_score += 2, narrative_score += 1, systems_score += 1
```

🔨 **Building projects and learning through iteration**
```
technical_score += 2, systems_score += 2, innovation_drive += 1
```

---

## Question 10: Rate your comfort level with these responsibilities
**Type: Opinion Scale (1-5 scale for each)**
*Comfort level with different responsibilities*

**Rate from 1 (Very uncomfortable) to 5 (Very comfortable):**

👥 **Managing people and their career development**
```
Scale 1-5: leadership_preference += (rating * 0.8), social_score += (rating * 0.4)
```

⏰ **Working under tight deadlines and pressure**
```
Scale 1-5: business_score += (rating * 0.4), detail_orientation += (rating * 0.3)
```

🎨 **Presenting creative work for critique and feedback**
```
Scale 1-5: creative_score += (rating * 0.4), visual_score += (rating * 0.3), social_score += (rating * 0.2)
```

🔧 **Debugging complex technical issues**
```
Scale 1-5: technical_score += (rating * 0.8), detail_orientation += (rating * 0.4)
```

💬 **Communicating with external stakeholders**
```
Scale 1-5: business_score += (rating * 0.6), social_score += (rating * 0.4)
```

---

## Question 11: What games do you find yourself drawn to?
**Type: Picture Choice with game screenshots**
*Game preferences reveal design sensibilities*

**Select 1-2 game types that you're most drawn to:**

🎮 **Technical showcases (realistic graphics, complex systems)**
```
technical_score += 2, visual_score += 2, detail_orientation += 1
```

🎨 **Artistic indies (unique visual style, creative gameplay)**
```
creative_score += 3, visual_score += 2, innovation_drive += 2
```

📖 **Story-rich RPGs (deep narrative, character development)**
```
narrative_score += 3, creative_score += 1, user_focus += 1
```

🎵 **Music/rhythm games (audio-focused, precision-based)**
```
audio_score += 3, detail_orientation += 2
```

👥 **Multiplayer competitive (community, balance, esports)**
```
social_score += 2, systems_score += 2, business_score += 1
```

📱 **Mobile/casual (accessible, broad appeal)**
```
business_score += 2, user_focus += 2, systems_score += 1
```

---

## Question 12: Final priority check
**Type: Ranking (All 6 items)**
*Final comprehensive ranking to validate and refine scores*

**Rank ALL of these from most important (1) to least important (6) for your ideal role:**

🎯 **Creative freedom and artistic expression**
```
1st-2nd: creative_score += 2, 3rd-4th: creative_score += 1, 5th-6th: no change
```

💰 **Financial success and career growth**
```
1st-2nd: business_score += 2, 3rd-4th: business_score += 1, 5th-6th: no change
```

🔧 **Technical excellence and innovation**
```
1st-2nd: technical_score += 2, 3rd-4th: technical_score += 1, 5th-6th: no change
```

❤️ **Meaningful impact on players**
```
1st-2nd: user_focus += 2, narrative_score += 1, 3rd-4th: user_focus += 1, 5th-6th: no change
```

🤝 **Collaborative team environment**
```
1st-2nd: social_score += 2, collaboration_style += 2, 3rd-4th: social_score += 1, 5th-6th: no change
```

👑 **Leadership and decision-making authority**
```
1st-2nd: leadership_preference += 2, business_score += 1, 3rd-4th: leadership_preference += 1, 5th-6th: no change
```

---

## Advanced Typeform Logic Features

### Progress Indicators
- Show progress based on question completion
- Add encouraging messages at 25%, 50%, 75% completion

### Conditional Logic Examples
```
IF technical_score > 15 AND Question_5_answer = "Solo work" 
THEN show: "What aspect of solo technical work appeals to you most?"

IF creative_score > 12 AND visual_score > 10 
THEN show: "Which visual medium excites you most? (2D, 3D, UI, etc.)"

IF social_score > 15 
THEN show: "How do you prefer to contribute to team dynamics?"
```

### Dynamic Question Text
```
"Based on your interest in [highest_category], how important is [related_skill] to you?"
```

### Result Personalization Variables
```
top_category = highest scoring category
secondary_category = second highest scoring category
work_style = combination of collaboration_style + leadership_preference
specialization_level = detail_orientation score
innovation_focus = innovation_drive score
```

This optimized structure uses the best Typeform question types for each data point while maintaining the sophisticated scoring system!