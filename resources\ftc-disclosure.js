// FTC Compliant Disclosure Language for Amazon Affiliate Links
// Natural, engaging disclosure text that fits the quiz tone

const ftcDisclosures = {
    // Main disclosure for book recommendation sections
    bookRecommendation: {
        short: "📚 As an Amazon Associate, I earn from qualifying purchases. This helps support the creation of more career resources like this quiz!",
        
        medium: "📚 <strong>Transparency Note:</strong> As an Amazon Associate, I earn from qualifying purchases when you buy books through these links. This doesn't cost you anything extra, but it helps me create more free career resources like this quiz. I only recommend books I genuinely believe will help your game development journey.",
        
        detailed: "📚 <strong>About These Book Recommendations:</strong> I've carefully selected these books based on their value to aspiring game developers. As an Amazon Associate, I earn a small commission from qualifying purchases made through these links at no additional cost to you. These commissions help support the time and research that goes into creating comprehensive career resources like this quiz. I only recommend books that I believe will genuinely help advance your career in game development."
    },

    // Footer disclosure for the entire quiz
    quizFooter: {
        standard: "This quiz contains affiliate links to books and resources. As an Amazon Associate, I earn from qualifying purchases. All recommendations are based on genuine value to game development careers.",
        
        friendly: "💡 <strong>Supporting This Resource:</strong> This free quiz is supported by affiliate commissions from book recommendations. When you purchase recommended books through our links, you help fund the creation of more career guidance resources. All book recommendations are chosen for their genuine value to game developers, not commission rates."
    },

    // Inline disclosure for individual book links
    inline: {
        minimal: "(affiliate link)",
        descriptive: "(Amazon affiliate link - supports this resource)",
        emoji: "🔗 (affiliate link)"
    },

    // Disclosure for the quiz introduction/about section
    aboutSection: {
        transparent: `
        <div class="disclosure-section">
            <h3>About This Quiz & Recommendations</h3>
            <p>This career quiz is designed to provide genuine, practical guidance for aspiring game developers. The recommendations are based on industry research, professional experience, and proven career paths.</p>
            
            <p><strong>Book Recommendations:</strong> I've carefully curated book recommendations that I believe will genuinely help your game development career. Some links to these books are Amazon affiliate links, which means I earn a small commission if you make a purchase (at no extra cost to you). These commissions help support the time and research required to maintain and improve this resource.</p>
            
            <p><strong>No Conflicts of Interest:</strong> I don't recommend specific courses, communities, or paid programs to avoid conflicts of interest. The focus is on practical, actionable advice and proven learning resources.</p>
        </div>
        `
    },

    // CSS for styling disclosure sections
    disclosureCSS: `
        .disclosure-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .disclosure-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.1rem;
        }

        .disclosure-section p {
            margin: 0 0 12px 0;
            color: #6c757d;
        }

        .disclosure-section p:last-child {
            margin-bottom: 0;
        }

        .affiliate-disclosure {
            background: #f8f9ff;
            border-left: 4px solid #667eea;
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 0 4px 4px 0;
        }

        .affiliate-disclosure small {
            color: #666;
            font-size: 0.85rem;
            line-height: 1.4;
            display: block;
        }

        .quiz-footer-disclosure {
            background: #f8f9fa;
            border-top: 2px solid #e9ecef;
            padding: 20px;
            margin-top: 40px;
            text-align: center;
            font-size: 0.9rem;
            color: #6c757d;
        }
    `,

    // JavaScript for dynamic disclosure insertion
    insertDisclosures: function(affiliateId) {
        return `
            // Insert appropriate disclosures based on context
            function insertFTCDisclosures() {
                // Add footer disclosure
                const quizContainer = document.querySelector('.quiz-container');
                if (quizContainer && !document.querySelector('.quiz-footer-disclosure')) {
                    const footerDisclosure = document.createElement('div');
                    footerDisclosure.className = 'quiz-footer-disclosure';
                    footerDisclosure.innerHTML = '${this.quizFooter.friendly}';
                    quizContainer.appendChild(footerDisclosure);
                }
                
                // Add disclosure to book sections
                const bookSections = document.querySelectorAll('.book-recommendation');
                bookSections.forEach(section => {
                    if (!section.querySelector('.affiliate-disclosure')) {
                        const disclosure = document.createElement('div');
                        disclosure.className = 'affiliate-disclosure';
                        disclosure.innerHTML = '<small>${this.bookRecommendation.short}</small>';
                        section.appendChild(disclosure);
                    }
                });
            }
            
            // Call when results are displayed
            insertFTCDisclosures();
        `;
    },

    // Generate compliant disclosure for specific contexts
    generateContextualDisclosure: function(context, length = 'medium') {
        const disclosures = {
            'book-section': this.bookRecommendation,
            'quiz-footer': this.quizFooter,
            'about-page': this.aboutSection,
            'inline': this.inline
        };

        const contextDisclosures = disclosures[context];
        if (!contextDisclosures) return this.bookRecommendation.medium;

        if (typeof contextDisclosures === 'string') {
            return contextDisclosures;
        }

        return contextDisclosures[length] || contextDisclosures.standard || contextDisclosures.medium;
    },

    // Validation helper to ensure compliance
    validateDisclosure: function(disclosureText) {
        const requiredElements = [
            'amazon associate',
            'earn',
            'qualifying purchases'
        ];

        const hasRequired = requiredElements.every(element => 
            disclosureText.toLowerCase().includes(element)
        );

        return {
            compliant: hasRequired,
            missing: requiredElements.filter(element => 
                !disclosureText.toLowerCase().includes(element)
            )
        };
    },

    // Generate disclosure for different quiz sections
    getQuizSectionDisclosure: function(section) {
        switch(section) {
            case 'results':
                return this.bookRecommendation.medium;
            case 'footer':
                return this.quizFooter.friendly;
            case 'about':
                return this.aboutSection.transparent;
            default:
                return this.bookRecommendation.short;
        }
    }
};

// Usage examples and integration helpers
const disclosureIntegration = {
    // Add to quiz results HTML
    addToResults: function(resultsHTML, affiliateId) {
        const disclosure = ftcDisclosures.generateContextualDisclosure('book-section');
        
        // Insert disclosure before closing div of results
        return resultsHTML.replace(
            '</div>',
            `<div class="affiliate-disclosure"><small>${disclosure}</small></div></div>`
        );
    },

    // Add CSS to page
    addCSS: function() {
        const style = document.createElement('style');
        style.textContent = ftcDisclosures.disclosureCSS;
        document.head.appendChild(style);
    },

    // Add footer disclosure
    addFooterDisclosure: function() {
        const container = document.querySelector('.quiz-container');
        if (container && !document.querySelector('.quiz-footer-disclosure')) {
            const footer = document.createElement('div');
            footer.className = 'quiz-footer-disclosure';
            footer.innerHTML = ftcDisclosures.quizFooter.friendly;
            container.appendChild(footer);
        }
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ftcDisclosures, disclosureIntegration };
}
