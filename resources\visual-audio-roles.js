// Visual and Audio Role Definitions for Game Dev Career Quiz
// Focus: Fundamentals-first approach, practical creative and technical guidance

const visualAudioRoles = {
    "Technical Artist": {
        description: "You're the bridge between art and code - the person who makes impossible visual ideas actually work in-engine. Like the technical artists who figured out how to make Breath of the <PERSON>'s art style run on Switch, or how to create Overwatch's stylized lighting system. You solve problems that pure artists and pure programmers can't.",
        
        strengths: [
            "Dual art/tech expertise",
            "Problem-solving creativity",
            "Pipeline optimization",
            "Cross-team communication"
        ],
        
        weaknesses: [
            "May be pulled in too many directions",
            "Risk of becoming a bottleneck",
            "Tendency to over-engineer artistic solutions"
        ],
        
        first30Days: [
            "Week 1: Learn basic shader programming - start with simple color modifications",
            "Week 2: Create a custom material that solves a specific artistic problem",
            "Week 3: Build a simple tool that automates a repetitive art task",
            "Week 4: Study how a favorite game achieves its distinctive visual style"
        ],
        
        portfolioEssentials: [
            "Custom shaders that solve specific artistic challenges",
            "Art pipeline tool that improves workflow efficiency",
            "Technical breakdown of how you achieved a complex visual effect",
            "Before/after examples showing technical solutions to art problems",
            "Cross-platform optimization examples with performance metrics"
        ],
        
        fundamentalSkills: [
            "Learn shader programming (HLSL/GLSL basics)",
            "Master 3D software scripting (Python for Maya/Blender)",
            "Understand rendering pipelines and optimization techniques",
            "Study color theory and lighting principles",
            "Practice clear technical communication with both artists and programmers"
        ],
        
        bookRecommendation: "Real-Time Rendering by Tomas Akenine-Möller",
        actionableAdvice: "Specialize in one area (shaders, rigging, or tools) while maintaining broad knowledge. Create documentation and tutorials to scale your knowledge. Build strong relationships with both art and programming teams."
    },

    "Visual Artist": {
        description: "You're the artist who brings game worlds to life through concept art, 3D models, textures, or animations. Whether creating the atmospheric environments of Hollow Knight, the character designs of Overwatch, or the visual effects of Diablo, your work directly shapes what players see and feel.",
        
        strengths: [
            "Strong artistic fundamentals",
            "Visual storytelling ability",
            "Attention to aesthetic detail",
            "Creative problem-solving"
        ],
        
        weaknesses: [
            "May struggle with technical constraints",
            "Risk of perfectionism over productivity",
            "Tendency to work in isolation"
        ],
        
        first30Days: [
            "Week 1: Create 10 thumbnail sketches exploring different visual approaches to one concept",
            "Week 2: Model and texture a simple game asset following technical constraints",
            "Week 3: Study lighting in 3 different games - recreate one lighting setup",
            "Week 4: Create a piece that tells a story without using any text"
        ],
        
        portfolioEssentials: [
            "Concept art that clearly communicates design ideas",
            "3D models optimized for real-time rendering",
            "Texture work showing understanding of materials and lighting",
            "Character or environment that demonstrates strong visual storytelling",
            "Technical art specs showing understanding of game constraints"
        ],
        
        fundamentalSkills: [
            "Master fundamental drawing and painting techniques",
            "Learn 3D modeling and texturing workflows",
            "Understand lighting and color theory",
            "Study anatomy, perspective, and composition",
            "Practice working within technical constraints (poly counts, texture budgets)"
        ],
        
        bookRecommendation: "Color and Light by James Gurney",
        actionableAdvice: "Build a diverse portfolio showcasing different styles and technical skills. Learn the technical pipeline of your chosen specialty. Seek feedback early and often - art is communication, not just self-expression."
    },

    "Audio Programmer": {
        description: "You're the programmer who makes audio systems work seamlessly in games. Like the engineers behind Wwise, FMOD, or the spatial audio in games like Hellblade, you combine technical precision with audio knowledge to solve problems that pure programmers or pure audio designers can't handle alone.",
        
        strengths: [
            "Audio system expertise",
            "Performance optimization",
            "Cross-platform audio knowledge",
            "Technical problem-solving"
        ],
        
        weaknesses: [
            "May over-optimize audio at expense of other systems",
            "Risk of building overly complex audio tools",
            "Tendency to work in technical isolation"
        ],
        
        first30Days: [
            "Week 1: Build a simple audio player that can load and play multiple formats",
            "Week 2: Implement basic 3D positional audio with distance falloff",
            "Week 3: Create a dynamic music system that responds to game events",
            "Week 4: Study how a favorite game implements its audio systems"
        ],
        
        portfolioEssentials: [
            "Custom audio engine with 3D positioning and effects",
            "Dynamic music system that adapts to gameplay",
            "Audio tool that simplifies complex audio implementation",
            "Performance analysis of audio systems under different loads",
            "Cross-platform audio solution with platform-specific optimizations"
        ],
        
        fundamentalSkills: [
            "Learn digital signal processing fundamentals",
            "Master audio programming APIs (DirectSound, Core Audio, etc.)",
            "Understand audio compression and streaming techniques",
            "Study psychoacoustics and spatial audio principles",
            "Practice optimizing audio performance and memory usage"
        ],
        
        bookRecommendation: "Designing Sound by Andy Farnell",
        actionableAdvice: "Stay current with audio middleware and emerging technologies. Build strong relationships with audio designers to understand their workflow needs. Focus on tools that empower creators, not just technical achievements."
    },

    "Audio Designer": {
        description: "You're the sound architect who crafts the audio landscape of games. Whether creating the atmospheric soundscapes of Limbo, the dynamic music systems of Nier: Automata, or the satisfying audio feedback of Overwatch, you understand that great audio design is felt, not just heard.",
        
        strengths: [
            "Musical and sound intuition",
            "Emotional design understanding",
            "Audio storytelling ability",
            "Technical audio skills"
        ],
        
        weaknesses: [
            "May over-design audio for simple interactions",
            "Risk of creating audio that fights gameplay",
            "Tendency to work without enough player feedback"
        ],
        
        first30Days: [
            "Week 1: Record and edit 20 different sound effects for common game actions",
            "Week 2: Create a 2-minute adaptive music track that changes based on intensity",
            "Week 3: Design the complete audio landscape for a simple game scene",
            "Week 4: Study how audio supports gameplay in 3 different game genres"
        ],
        
        portfolioEssentials: [
            "Sound effect library showing range and technical quality",
            "Interactive music system that responds to player actions",
            "Complete audio design for a game scene or level",
            "Audio implementation examples showing technical understanding",
            "Demonstration of audio's impact on player emotion and behavior"
        ],
        
        fundamentalSkills: [
            "Master digital audio workstation (DAW) software",
            "Learn field recording and sound design techniques",
            "Understand music theory and composition",
            "Study audio implementation in game engines",
            "Practice mixing and mastering for interactive media"
        ],
        
        bookRecommendation: "Audio for Games: Planning, Process, and Production by Alexander Brandon",
        actionableAdvice: "Study how audio affects player psychology and behavior. Build a diverse sound library and learn multiple audio tools. Always test your audio in actual gameplay contexts, not just in isolation."
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = visualAudioRoles;
}
